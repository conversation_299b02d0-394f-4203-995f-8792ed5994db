package cn.iocoder.yudao.module.visitor.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 培训状态枚举
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Getter
@AllArgsConstructor
public enum TrainingStatusEnum{

    NOT_STARTED(0, "未开始"),
    COMPLETED(1, "已完成"),
    EXPIRED(2, "已过期");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(TrainingStatusEnum::getStatus).toArray();

    /**
     * 状态值
     */
    private final Integer status;
    /**
     * 状态名称
     */
    private final String name;

    public static TrainingStatusEnum valueOf(Integer status) {
        return Arrays.stream(values()).filter(item -> item.getStatus().equals(status)).findFirst().orElse(null);
    }

}
