package cn.iocoder.yudao.module.visitor.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 审批结果枚举
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Getter
@AllArgsConstructor
public enum ApprovalResultEnum{

    APPROVED(1, "通过"),
    REJECTED(2, "驳回");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(ApprovalResultEnum::getResult).toArray();

    /**
     * 审批结果
     */
    private final Integer result;
    /**
     * 审批结果名称
     */
    private final String name;

    public static ApprovalResultEnum valueOf(Integer result) {
        return Arrays.stream(values()).filter(item -> item.getResult().equals(result)).findFirst().orElse(null);
    }

}
