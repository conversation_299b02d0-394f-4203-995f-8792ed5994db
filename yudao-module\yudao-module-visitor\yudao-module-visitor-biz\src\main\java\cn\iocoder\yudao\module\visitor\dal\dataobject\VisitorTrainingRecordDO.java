package cn.iocoder.yudao.module.visitor.dal.dataobject;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.visitor.enums.TrainingStatusEnum;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 访客培训完成记录 DO
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@TableName(value = "visitor_training_record", autoResultMap = true)
@KeySequence("visitor_training_record_seq") // 鐢ㄤ簬 Oracle銆丳ostgreSQL銆並ingbase銆丏B2銆丠2 鏁版嵁搴撶殑涓婚敭鑷銆傚鏋滄槸 MySQL 绛夋暟鎹簱锛屽彲涓嶅啓
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VisitorTrainingRecordDO extends BaseDO {

    /**
     * 涓婚敭ID
     */
    @TableId
    private Long id;

    /**
     * 申请鍗旾D
     */
    private Long applicationId;

    /**
     * 培训ID
     */
    private Long trainingId;

    /**
     * 访客姓名
     */
    private String visitorName;

    /**
     * 开始嬫椂闂?     */
    private LocalDateTime startTime;

    /**
     * 缁撴潫时间
     */
    private LocalDateTime endTime;

    /**
     * 瀹為檯培训时长锛堝垎閽燂級
     */
    private Integer duration;

    /**
     * 完成状态?     *
     * 鏋氫妇 {@link TrainingStatusEnum}
     */
    private Integer completionStatus;

    /**
     * 考试寰楀垎
     */
    private Integer examScore;

    /**
     * 考试绛旀JSON
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<ExamAnswer> examAnswers;

    /**
     * 绛惧瓧鍥剧墖URL
     */
    private String signatureImage;

    /**
     * 璇佷功URL
     */
    private String certificateUrl;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 鐢ㄦ埛浠ｇ悊
     */
    private String userAgent;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 考试绛旀鍐呴儴绫?     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExamAnswer {
        /**
         * 棰樼洰ID
         */
        private String questionId;
        /**
         * 鐢ㄦ埛绛旀
         */
        private List<String> userAnswers;
        /**
         * 鏄惁姝ｇ‘
         */
        private Boolean isCorrect;
        /**
         * 寰楀垎
         */
        private Integer score;
    }

}

