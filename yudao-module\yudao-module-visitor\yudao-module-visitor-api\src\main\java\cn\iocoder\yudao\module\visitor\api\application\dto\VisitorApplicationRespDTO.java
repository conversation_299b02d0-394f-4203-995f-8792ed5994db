package cn.iocoder.yudao.module.visitor.api.application.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 访客申请 Response DTO
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Data
public class VisitorApplicationRespDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 申请单号
     */
    private String applicationNo;

    /**
     * 访客类型
     */
    private Integer visitorType;

    /**
     * 来访单位
     */
    private String companyName;

    /**
     * 访客姓名
     */
    private String visitorName;

    /**
     * 联系方式
     */
    private String visitorPhone;

    /**
     * 身份证号码
     */
    private String idCard;

    /**
     * 访客照片URL
     */
    private String visitorPhoto;

    /**
     * 来访事由
     */
    private String visitReason;

    /**
     * 访问目的
     */
    private Integer visitPurpose;

    /**
     * 厂内联系人
     */
    private String contactPerson;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 联系人部门ID
     */
    private Long contactDeptId;

    /**
     * 联系人部门名称
     */
    private String contactDeptName;

    /**
     * 预计到访开始时间
     */
    private LocalDateTime visitStartTime;

    /**
     * 预计到访结束时间
     */
    private LocalDateTime visitEndTime;

    /**
     * 到访厂区
     */
    private String visitArea;

    /**
     * 到访方式
     */
    private Integer visitMethod;

    /**
     * 是否驾车
     */
    private Integer hasVehicle;

    /**
     * 车牌号
     */
    private String vehiclePlate;

    /**
     * 车辆类型
     */
    private Integer vehicleType;

    /**
     * 行驶证编号
     */
    private String vehicleLicense;

    /**
     * 车辆照片URL
     */
    private String vehiclePhoto;

    /**
     * 同行人数量
     */
    private Integer companionCount;

    /**
     * 同行人信息
     */
    private List<CompanionInfo> companionInfo;

    /**
     * 是否需要住宿
     */
    private Integer needAccommodation;

    /**
     * 是否前往饭堂就餐
     */
    private Integer needDining;

    /**
     * 是否完成培训
     */
    private Integer trainingCompleted;

    /**
     * 培训完成时间
     */
    private LocalDateTime trainingTime;

    /**
     * 培训签字图片URL
     */
    private String trainingSignature;

    /**
     * 通行二维码URL
     */
    private String qrCode;

    /**
     * 二维码内容
     */
    private String qrCodeContent;

    /**
     * 二维码过期时间
     */
    private LocalDateTime qrCodeExpireTime;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 审批结果
     */
    private Integer approvalResult;

    /**
     * 审批意见
     */
    private String approvalReason;

    /**
     * 审批时间
     */
    private LocalDateTime approvalTime;

    /**
     * 审批人ID
     */
    private Long approverId;

    /**
     * 审批人姓名
     */
    private String approverName;

    /**
     * 入园时间
     */
    private LocalDateTime entryTime;

    /**
     * 出园时间
     */
    private LocalDateTime exitTime;

    /**
     * 入园操作员ID
     */
    private Long entryOperatorId;

    /**
     * 出园操作员ID
     */
    private Long exitOperatorId;

    /**
     * Flowable流程实例ID
     */
    private String processInstanceId;

    /**
     * 承包商项目ID
     */
    private Long contractorProjectId;

    /**
     * 紧急联系人
     */
    private String emergencyContact;

    /**
     * 紧急联系电话
     */
    private String emergencyPhone;

    /**
     * 特殊要求
     */
    private String specialRequirements;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 同行人信息内部类
     */
    @Data
    public static class CompanionInfo {
        /**
         * 姓名
         */
        private String name;
        /**
         * 电话
         */
        private String phone;
        /**
         * 身份证号
         */
        private String idCard;
        /**
         * 照片URL
         */
        private String photo;
    }

}
