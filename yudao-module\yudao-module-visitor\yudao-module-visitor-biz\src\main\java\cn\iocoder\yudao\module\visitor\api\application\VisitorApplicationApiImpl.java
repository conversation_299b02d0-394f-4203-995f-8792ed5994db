package cn.iocoder.yudao.module.visitor.api.application;

import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.visitor.api.application.dto.VisitorApplicationRespDTO;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorApplicationDO;
import cn.iocoder.yudao.module.visitor.service.application.VisitorApplicationService;
import cn.iocoder.yudao.module.visitor.service.record.VisitorRecordService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.visitor.enums.ErrorCodeConstants.VISITOR_APPLICATION_NOT_EXISTS;

/**
 * 访客申请 API 实现类
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Service
@Slf4j
public class VisitorApplicationApiImpl implements VisitorApplicationApi {

    @Resource
    private VisitorApplicationService visitorApplicationService;

    @Resource
    private VisitorRecordService visitorRecordService;

    @Override
    public VisitorApplicationRespDTO getVisitorApplication(Long id) {
        VisitorApplicationDO application = visitorApplicationService.getVisitorApplication(id);
        return BeanUtils.toBean(application, VisitorApplicationRespDTO.class);
    }

    @Override
    public List<VisitorApplicationRespDTO> getVisitorApplicationList(Collection<Long> ids) {
        List<VisitorApplicationDO> list = visitorApplicationService.getVisitorApplicationList(ids);
        return BeanUtils.toBean(list, VisitorApplicationRespDTO.class);
    }

    @Override
    public VisitorApplicationRespDTO getVisitorApplicationByNo(String applicationNo) {
        VisitorApplicationDO application = visitorApplicationService.getVisitorApplicationByNo(applicationNo);
        return BeanUtils.toBean(application, VisitorApplicationRespDTO.class);
    }

    @Override
    public void validateVisitorApplicationExists(Long id) {
        VisitorApplicationDO application = visitorApplicationService.getVisitorApplication(id);
        if (application == null) {
            throw exception(VISITOR_APPLICATION_NOT_EXISTS);
        }
    }

    @Override
    public Long getCurrentVisitorCount() {
        return visitorRecordService.getCurrentVisitorCount();
    }

    @Override
    public VisitorApplicationRespDTO getVisitorApplicationByProcessInstanceId(String processInstanceId) {
        VisitorApplicationDO application = visitorApplicationService.getVisitorApplicationByProcessInstanceId(processInstanceId);
        return BeanUtils.toBean(application, VisitorApplicationRespDTO.class);
    }

    @Override
    public Integer getVisitorApplicationStatus(Long id) {
        VisitorApplicationDO application = visitorApplicationService.getVisitorApplication(id);
        return application != null ? application.getStatus() : null;
    }

    @Override
    public void updateVisitorApplicationStatus(Long id, Integer status, String reason) {
        visitorApplicationService.updateVisitorApplicationStatus(id, status, reason);
    }

    @Override
    public String getVisitorQrCodeContent(Long id) {
        VisitorApplicationDO application = visitorApplicationService.getVisitorApplication(id);
        return application != null ? application.getQrCodeContent() : null;
    }

    @Override
    public boolean hasAreaPermission(Long applicationId, String area) {
        VisitorApplicationDO application = visitorApplicationService.getVisitorApplication(applicationId);
        if (application == null) {
            return false;
        }

        // 检查访客申请的访问区域是否包含指定区域
        return application.getVisitArea() != null && application.getVisitArea().contains(area);
    }

}

