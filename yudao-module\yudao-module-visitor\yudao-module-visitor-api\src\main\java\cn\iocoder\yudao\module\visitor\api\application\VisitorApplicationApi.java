package cn.iocoder.yudao.module.visitor.api.application;

import cn.iocoder.yudao.module.visitor.api.application.dto.VisitorApplicationRespDTO;

import java.util.Collection;
import java.util.List;

/**
 * 璁垮鐢宠 API 鎺ュ彛
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
public interface VisitorApplicationApi {

    /**
     * 鑾峰緱璁垮鐢宠
     *
     * @param id 缂栧彿
     * @return 璁垮鐢宠
     */
    VisitorApplicationRespDTO getVisitorApplication(Long id);

    /**
     * 鑾峰緱璁垮鐢宠鍒楄〃
     *
     * @param ids 缂栧彿
     * @return 璁垮鐢宠鍒楄〃
     */
    List<VisitorApplicationRespDTO> getVisitorApplicationList(Collection<Long> ids);

    /**
     * 鏍规嵁鐢宠鍗曞彿鑾峰彇璁垮鐢宠
     *
     * @param applicationNo 鐢宠鍗曞彿
     * @return 璁垮鐢宠
     */
    VisitorApplicationRespDTO getVisitorApplicationByNo(String applicationNo);

    /**
     * 鏍￠獙璁垮鐢宠鏄惁瀛樺湪
     *
     * @param id 缂栧彿
     */
    void validateVisitorApplicationExists(Long id);

    /**
     * 鑾峰彇褰撳墠鍦ㄥ洯璁垮鏁伴噺
     *
     * @return 鍦ㄥ洯璁垮鏁伴噺
     */
    Long getCurrentVisitorCount();

    /**
     * 妫€鏌ヨ瀹㈡槸鍚︽湁鏉冮檺杩涘叆鎸囧畾鍖哄煙
     *
     * @param applicationId 鐢宠ID
     * @param area 鍖哄煙
     * @return 鏄惁鏈夋潈闄?     */
    boolean hasAreaPermission(Long applicationId, String area);

    /**
     * 鏍规嵁娴佺▼瀹炰緥ID鑾峰彇璁垮鐢宠
     *
     * @param processInstanceId 娴佺▼瀹炰緥ID
     * @return 璁垮鐢宠
     */
    VisitorApplicationRespDTO getVisitorApplicationByProcessInstanceId(String processInstanceId);

    /**
     * 鑾峰彇璁垮鐢宠鐘舵€?     *
     * @param id 鐢宠ID
     * @return 鐘舵€?     */
    Integer getVisitorApplicationStatus(Long id);

    /**
     * 鏇存柊璁垮鐢宠鐘舵€?     *
     * @param id 鐢宠ID
     * @param status 鏂扮姸鎬?     * @param reason 鍙樻洿鍘熷洜
     */
    void updateVisitorApplicationStatus(Long id, Integer status, String reason);

    /**
     * 鑾峰彇璁垮浜岀淮鐮佸唴瀹?     *
     * @param id 鐢宠ID
     * @return 浜岀淮鐮佸唴瀹?     */
    String getVisitorQrCodeContent(Long id);

}
