package cn.iocoder.yudao.module.visitor.controller.app.guard;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import org.springframework.security.access.prepost.PreAuthorize;
import cn.iocoder.yudao.module.visitor.controller.app.guard.vo.AppQrCodeScanReqVO;
import cn.iocoder.yudao.module.visitor.controller.app.guard.vo.AppQrCodeScanRespVO;
import cn.iocoder.yudao.module.visitor.controller.app.guard.vo.AppVisitorCheckInReqVO;
import cn.iocoder.yudao.module.visitor.controller.app.guard.vo.AppVisitorCheckOutReqVO;
import cn.iocoder.yudao.module.visitor.service.application.VisitorApplicationService;
import cn.iocoder.yudao.module.visitor.service.record.VisitorRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 鐢ㄦ埛 APP - 璀﹀崼操作员 Controller
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Tag(name = "鐢ㄦ埛 APP - 璀﹀崼操作员")
@RestController
@RequestMapping("/app-api/visitor/guard")
@Validated
@Slf4j
public class AppGuardController {

    @Resource
    private VisitorApplicationService visitorApplicationService;

    @Resource
    private VisitorRecordService visitorRecordService;

    @PostMapping("/scan")
    @Operation(summary = "鎵弿访客二维码?)
    @PreAuthorize("hasRole('ROLE_USER')")
    public CommonResult<AppQrCodeScanRespVO> scanVisitorQrCode(@Valid @RequestBody AppQrCodeScanReqVO reqVO) {
        log.info("[scanVisitorQrCode] 鎵弿访客二维码侊紝鍐呭锛歿}", reqVO.getQrCodeContent());

        AppQrCodeScanRespVO result = visitorApplicationService.scanQrCode(reqVO.getQrCodeContent());
        return success(result);
    }

    @PostMapping("/check-in")
    @Operation(summary = "访客入园登记")
    @PreAuthorize("hasRole('ROLE_USER')")
    public CommonResult<Boolean> visitorCheckIn(@Valid @RequestBody AppVisitorCheckInReqVO reqVO) {
        log.info("[visitorCheckIn] 访客入园登记锛岀敵璇稩D锛歿}", reqVO.getApplicationId());

        visitorRecordService.checkIn(reqVO);
        return success(true);
    }

    @PostMapping("/check-out")
    @Operation(summary = "访客出园登记")
    @PreAuthorize("hasRole('ROLE_USER')")
    public CommonResult<Boolean> visitorCheckOut(@Valid @RequestBody AppVisitorCheckOutReqVO reqVO) {
        log.info("[visitorCheckOut] 访客出园登记锛岀敵璇稩D锛歿}", reqVO.getApplicationId());

        visitorRecordService.checkOut(reqVO);
        return success(true);
    }

}

