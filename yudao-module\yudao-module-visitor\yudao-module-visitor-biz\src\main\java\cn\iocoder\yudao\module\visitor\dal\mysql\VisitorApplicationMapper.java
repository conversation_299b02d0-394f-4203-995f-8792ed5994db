package cn.iocoder.yudao.module.visitor.dal.mysql;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.visitor.controller.admin.application.vo.VisitorApplicationPageReqVO;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorApplicationDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * 访客申请 Mapper
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Mapper
public interface VisitorApplicationMapper extends BaseMapperX<VisitorApplicationDO> {

    default PageResult<VisitorApplicationDO> selectPage(VisitorApplicationPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<VisitorApplicationDO>()
                .likeIfPresent(VisitorApplicationDO::getApplicationNo, reqVO.getApplicationNo())
                .eqIfPresent(VisitorApplicationDO::getVisitorType, reqVO.getVisitorType())
                .likeIfPresent(VisitorApplicationDO::getVisitorName, reqVO.getVisitorName())
                .likeIfPresent(VisitorApplicationDO::getVisitorPhone, reqVO.getVisitorPhone())
                .likeIfPresent(VisitorApplicationDO::getCompanyName, reqVO.getCompanyName())
                .likeIfPresent(VisitorApplicationDO::getContactPerson, reqVO.getContactPerson())
                .eqIfPresent(VisitorApplicationDO::getContactDeptId, reqVO.getContactDeptId())
                .eqIfPresent(VisitorApplicationDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(VisitorApplicationDO::getVisitStartTime, reqVO.getVisitStartTime())
                .betweenIfPresent(VisitorApplicationDO::getVisitEndTime, reqVO.getVisitEndTime())
                .betweenIfPresent(VisitorApplicationDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(VisitorApplicationDO::getId));
    }

    default List<VisitorApplicationDO> selectList(VisitorApplicationPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<VisitorApplicationDO>()
                .likeIfPresent(VisitorApplicationDO::getApplicationNo, reqVO.getApplicationNo())
                .eqIfPresent(VisitorApplicationDO::getVisitorType, reqVO.getVisitorType())
                .likeIfPresent(VisitorApplicationDO::getVisitorName, reqVO.getVisitorName())
                .likeIfPresent(VisitorApplicationDO::getVisitorPhone, reqVO.getVisitorPhone())
                .likeIfPresent(VisitorApplicationDO::getCompanyName, reqVO.getCompanyName())
                .likeIfPresent(VisitorApplicationDO::getContactPerson, reqVO.getContactPerson())
                .eqIfPresent(VisitorApplicationDO::getContactDeptId, reqVO.getContactDeptId())
                .eqIfPresent(VisitorApplicationDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(VisitorApplicationDO::getVisitStartTime, reqVO.getVisitStartTime())
                .betweenIfPresent(VisitorApplicationDO::getVisitEndTime, reqVO.getVisitEndTime())
                .betweenIfPresent(VisitorApplicationDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(VisitorApplicationDO::getId));
    }

    default VisitorApplicationDO selectByApplicationNo(String applicationNo) {
        return selectOne(VisitorApplicationDO::getApplicationNo, applicationNo);
    }

    default VisitorApplicationDO selectByProcessInstanceId(String processInstanceId) {
        return selectOne(VisitorApplicationDO::getProcessInstanceId, processInstanceId);
    }

    default List<VisitorApplicationDO> selectListByStatus(Collection<Integer> statuses) {
        return selectList(new LambdaQueryWrapperX<VisitorApplicationDO>()
                .inIfPresent(VisitorApplicationDO::getStatus, statuses));
    }

    default List<VisitorApplicationDO> selectListByContactDeptId(Long contactDeptId) {
        return selectList(VisitorApplicationDO::getContactDeptId, contactDeptId);
    }

    default List<VisitorApplicationDO> selectListByVisitTime(LocalDateTime startTime, LocalDateTime endTime) {
        return selectList(new LambdaQueryWrapperX<VisitorApplicationDO>()
                .ge(VisitorApplicationDO::getVisitStartTime, startTime)
                .le(VisitorApplicationDO::getVisitEndTime, endTime));
    }

    default List<VisitorApplicationDO> selectExpiredApplications(LocalDateTime currentTime) {
        return selectList(new LambdaQueryWrapperX<VisitorApplicationDO>()
                .lt(VisitorApplicationDO::getVisitEndTime, currentTime)
                .in(VisitorApplicationDO::getStatus, 1, 2, 4)); // 寰呯‘璁ゃ€佸凡瀹℃壒銆佸凡入园状态佺殑杩囨湡申请
    }

    /**
     * 统计访客申请鏁伴噺鎸夌姸鎬佸垎缁?     */
    List<Object[]> selectCountGroupByStatus();

    /**
     * 统计访客申请鏁伴噺鎸夎瀹㈢被鍨嬪垎缁?     */
    List<Object[]> selectCountGroupByVisitorType();

    /**
     * 统计访客申请鏁伴噺鎸夐儴闂ㄥ垎缁?     */
    List<Object[]> selectCountGroupByDept();

    /**
     * 查询鎸囧畾时间鑼冨洿鍐呯殑访客申请统计
     */
    Long selectCountByTimeRange(@Param("startTime") LocalDateTime startTime, 
                               @Param("endTime") LocalDateTime endTime,
                               @Param("status") Integer status);

}

