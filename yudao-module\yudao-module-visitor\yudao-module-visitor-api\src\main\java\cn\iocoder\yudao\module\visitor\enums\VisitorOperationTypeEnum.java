package cn.iocoder.yudao.module.visitor.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 访客流程操作类型枚举
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Getter
@AllArgsConstructor
public enum VisitorOperationTypeEnum{

    SUBMIT_APPLICATION(1, "提交申请"),
    CONTACT_CONFIRM(2, "联系人确认"),
    DEPT_APPROVAL(3, "部门审批"),
    GENERAL_APPROVAL(4, "综管部审批"),
    SECURITY_APPROVAL(5, "安全部审批"),
    GENERATE_QR_CODE(6, "生成二维码"),
    ENTRY_REGISTER(7, "入园登记"),
    EXIT_REGISTER(8, "出园登记"),
    PROCESS_CANCEL(9, "流程取消"),
    PROCESS_REJECT(10, "流程驳回");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(VisitorOperationTypeEnum::getType).toArray();

    /**
     * 操作类型
     */
    private final Integer type;
    /**
     * 操作类型名称
     */
    private final String name;

    public static VisitorOperationTypeEnum valueOf(Integer type) {
        return Arrays.stream(values()).filter(item -> item.getType().equals(type)).findFirst().orElse(null);
    }

}
