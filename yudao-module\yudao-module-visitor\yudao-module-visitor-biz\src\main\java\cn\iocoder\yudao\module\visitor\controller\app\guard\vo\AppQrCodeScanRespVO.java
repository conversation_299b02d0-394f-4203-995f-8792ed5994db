package cn.iocoder.yudao.module.visitor.controller.app.guard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 鐢ㄦ埛 APP - 二维码佹壂鎻?Response VO
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Schema(description = "鐢ㄦ埛 APP - 二维码佹壂鎻?Response VO")
@Data
public class AppQrCodeScanRespVO {

    @Schema(description = "验证缁撴灉", example = "true")
    private Boolean valid;

    @Schema(description = "验证娑堟伅", example = "验证成功")
    private String message;

    @Schema(description = "申请ID", example = "1024")
    private Long applicationId;

    @Schema(description = "申请鍗曞彿", example = "V202501120001")
    private String applicationNo;

    @Schema(description = "访客姓名", example = "寮犱笁")
    private String visitorName;

    @Schema(description = "访客电话", example = "13800138000")
    private String visitorPhone;

    @Schema(description = "鏉ヨ鍗曚綅", example = "ABC鍏徃")
    private String companyName;

    @Schema(description = "鏉ヨ浜嬬敱", example = "鍟嗗姟娲借皥")
    private String visitReason;

    @Schema(description = "璁块棶鍖哄煙", example = "A鍖?)
    private String visitArea;

    @Schema(description = "联系人?, example = "鏉庡洓")
    private String contactPerson;

    @Schema(description = "联系人虹數璇?, example = "13900139000")
    private String contactPhone;

    @Schema(description = "棰勮鍒拌开始嬫椂闂?)
    private LocalDateTime visitStartTime;

    @Schema(description = "棰勮鍒拌缁撴潫时间")
    private LocalDateTime visitEndTime;

    @Schema(description = "鏄惁椹捐溅", example = "true")
    private Boolean hasVehicle;

    @Schema(description = "车牌鍙?, example = "绮12345")
    private String vehiclePlate;

    @Schema(description = "同行人浜哄憳信息")
    private List<CompanionInfo> companions;

    @Schema(description = "鏄惁已完成愬煿璁?, example = "true")
    private Boolean trainingCompleted;

    @Schema(description = "二维码佽繃鏈熸椂闂?)
    private LocalDateTime qrCodeExpireTime;

    @Schema(description = "同行人浜哄憳信息")
    @Data
    public static class CompanionInfo {
        @Schema(description = "姓名", example = "鐜嬩簲")
        private String name;

        @Schema(description = "电话", example = "13700137000")
        private String phone;

        @Schema(description = "韬唤璇佸彿", example = "******************")
        private String idCard;
    }

}

