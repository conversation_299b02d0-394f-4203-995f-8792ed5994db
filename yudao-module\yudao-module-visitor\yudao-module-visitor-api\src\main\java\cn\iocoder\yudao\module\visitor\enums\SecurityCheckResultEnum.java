package cn.iocoder.yudao.module.visitor.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 安检结果枚举
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Getter
@AllArgsConstructor
public enum SecurityCheckResultEnum{

    PASS(1, "通过"),
    FAIL(2, "未通过");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(SecurityCheckResultEnum::getResult).toArray();

    /**
     * 安检结果
     */
    private final Integer result;
    /**
     * 安检结果名称
     */
    private final String name;

    public static SecurityCheckResultEnum valueOf(Integer result) {
        return Arrays.stream(values()).filter(item -> item.getResult().equals(result)).findFirst().orElse(null);
    }

}
