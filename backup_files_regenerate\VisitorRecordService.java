package cn.iocoder.yudao.module.visitor.service.record;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.visitor.controller.admin.record.vo.VisitorRecordCreateReqVO;
import cn.iocoder.yudao.module.visitor.controller.admin.record.vo.VisitorRecordPageReqVO;
import cn.iocoder.yudao.module.visitor.controller.admin.record.vo.VisitorRecordUpdateReqVO;
import cn.iocoder.yudao.module.visitor.controller.app.guard.vo.AppVisitorCheckInReqVO;
import cn.iocoder.yudao.module.visitor.controller.app.guard.vo.AppVisitorCheckOutReqVO;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorRecordDO;
import jakarta.validation.Valid;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * 访客进出记录 Service 接口
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
public interface VisitorRecordService {

    /**
     * 创建访客进出记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createVisitorRecord(@Valid VisitorRecordCreateReqVO createReqVO);

    /**
     * 更新访客进出记录
     *
     * @param updateReqVO 更新信息
     */
    void updateVisitorRecord(@Valid VisitorRecordUpdateReqVO updateReqVO);

    /**
     * 删除访客进出记录
     *
     * @param id 编号
     */
    void deleteVisitorRecord(Long id);

    /**
     * 获得访客进出记录
     *
     * @param id 编号
     * @return 访客进出记录
     */
    VisitorRecordDO getVisitorRecord(Long id);

    /**
     * 获得访客进出记录列表
     *
     * @param ids 编号
     * @return 访客进出记录列表
     */
    List<VisitorRecordDO> getVisitorRecordList(Collection<Long> ids);

    /**
     * 获得访客进出记录分页
     *
     * @param pageReqVO 分页查询
     * @return 访客进出记录分页
     */
    PageResult<VisitorRecordDO> getVisitorRecordPage(VisitorRecordPageReqVO pageReqVO);

    /**
     * 获得访客进出记录列表, 鐢ㄤ簬 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 访客进出记录列表
     */
    List<VisitorRecordDO> getVisitorRecordList(VisitorRecordPageReqVO exportReqVO);

    /**
     * 鏍规嵁申请ID鑾峰彇进出记录
     *
     * @param applicationId 申请ID
     * @return 进出记录列表
     */
    List<VisitorRecordDO> getVisitorRecordsByApplicationId(Long applicationId);

    /**
     * 访客入园记录
     *
     * @param applicationId 申请ID
     * @param operatorId 操作员業D
     * @param gateLocation 门岗浣嶇疆
     * @param verificationMethod 验证鏂瑰紡
     * @param temperature 浣撴俯
     * @param remarks 备注
     * @return 记录ID
     */
    Long recordVisitorEntry(Long applicationId, Long operatorId, String gateLocation,
                           Integer verificationMethod, Double temperature, String remarks);

    /**
     * 访客出园记录
     *
     * @param applicationId 申请ID
     * @param operatorId 操作员業D
     * @param gateLocation 门岗浣嶇疆
     * @param remarks 备注
     * @return 记录ID
     */
    Long recordVisitorExit(Long applicationId, Long operatorId, String gateLocation, String remarks);

    /**
     * 鑾峰彇褰撳墠鍦ㄥ洯访客鏁伴噺
     *
     * @return 鍦ㄥ洯访客鏁伴噺
     */
    Long getCurrentVisitorCount();

    /**
     * 鑾峰彇访客进出统计
     *
     * @param startTime 开始嬫椂闂?     * @param endTime 缁撴潫时间
     * @return 统计鏁版嵁
     */
    Object getVisitorRecordStatistics(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 访客入园登记
     *
     * @param reqVO 入园登记璇锋眰
     */
    void checkIn(AppVisitorCheckInReqVO reqVO);

    /**
     * 访客出园登记
     *
     * @param reqVO 出园登记璇锋眰
     */
    void checkOut(AppVisitorCheckOutReqVO reqVO);

}

