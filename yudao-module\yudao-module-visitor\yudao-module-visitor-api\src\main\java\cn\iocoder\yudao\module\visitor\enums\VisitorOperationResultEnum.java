package cn.iocoder.yudao.module.visitor.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 访客流程操作结果枚举
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Getter
@AllArgsConstructor
public enum VisitorOperationResultEnum {

    SUCCESS(1, "成功"),
    FAILED(2, "失败"),
    CANCELLED(3, "取消"),
    RETURNED(4, "退回修改");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(VisitorOperationResultEnum::getResult).toArray();

    /**
     * 操作结果
     */
    private final Integer result;
    /**
     * 操作结果名称
     */
    private final String name;

    public static VisitorOperationResultEnum valueOf(Integer result) {
        return Arrays.stream(values()).filter(item -> item.getResult().equals(result)).findFirst().orElse(null);
    }

}
