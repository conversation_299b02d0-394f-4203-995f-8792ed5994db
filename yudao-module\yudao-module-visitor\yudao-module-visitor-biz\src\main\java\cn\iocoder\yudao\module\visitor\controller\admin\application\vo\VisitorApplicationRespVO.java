package cn.iocoder.yudao.module.visitor.controller.admin.application.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 管理后台 - 访客申请 Response VO
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Schema(description = "管理后台 - 访客申请 Response VO")
@Data
public class VisitorApplicationRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "访客类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer visitorType;

    @Schema(description = "来访单位", requiredMode = Schema.RequiredMode.REQUIRED, example = "某某公司")
    private String companyName;

    @Schema(description = "访客姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String visitorName;

    @Schema(description = "联系方式", requiredMode = Schema.RequiredMode.REQUIRED, example = "13800138000")
    private String visitorPhone;

    @Schema(description = "身份证号码", example = "110101199001011234")
    private String idCard;

    @Schema(description = "访客照片URL", example = "https://example.com/photo.jpg")
    private String visitorPhoto;

    @Schema(description = "来访事由", requiredMode = Schema.RequiredMode.REQUIRED, example = "商务洽谈")
    private String visitReason;

    @Schema(description = "访问目的", example = "2")
    private Integer visitPurpose;

    @Schema(description = "厂内联系人", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    private String contactPerson;

    @Schema(description = "联系人电话", requiredMode = Schema.RequiredMode.REQUIRED, example = "13900139000")
    private String contactPhone;

    @Schema(description = "联系人部门ID", example = "1")
    private Long contactDeptId;

    @Schema(description = "联系人部门名称", example = "技术部")
    private String contactDeptName;

    @Schema(description = "预计到访开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime visitStartTime;

    @Schema(description = "预计到访结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime visitEndTime;

    @Schema(description = "到访厂区", requiredMode = Schema.RequiredMode.REQUIRED, example = "A区")
    private String visitArea;

    @Schema(description = "到访方式", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer visitMethod;

    @Schema(description = "是否驾车", example = "0")
    private Integer hasVehicle;

    @Schema(description = "车牌号", example = "京A12345")
    private String vehiclePlate;

    @Schema(description = "车辆类型", example = "1")
    private Integer vehicleType;

    @Schema(description = "行驶证编号", example = "123456789")
    private String vehicleLicense;

    @Schema(description = "车辆照片URL", example = "https://example.com/vehicle.jpg")
    private String vehiclePhoto;

    @Schema(description = "同行人数量", example = "2")
    private Integer companionCount;

    @Schema(description = "同行人信息")
    private List<CompanionInfo> companionInfo;

    @Schema(description = "是否需要住宿", example = "0")
    private Integer needAccommodation;

    @Schema(description = "是否前往饭堂就餐", example = "0")
    private Integer needDining;

    @Schema(description = "紧急联系人", example = "王五")
    private String emergencyContact;

    @Schema(description = "紧急联系电话", example = "13700137000")
    private String emergencyPhone;

    @Schema(description = "特殊要求", example = "需要轮椅通道")
    private String specialRequirements;

    @Schema(description = "申请状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

    @Schema(description = "审批人ID", example = "1")
    private Long approverId;

    @Schema(description = "审批人姓名", example = "管理员")
    private String approverName;

    @Schema(description = "审批时间")
    private LocalDateTime approveTime;

    @Schema(description = "审批意见", example = "同意访问")
    private String approveReason;

    @Schema(description = "联系人确认状态", example = "1")
    private Integer confirmStatus;

    @Schema(description = "联系人确认时间")
    private LocalDateTime confirmTime;

    @Schema(description = "联系人确认意见", example = "同意接待")
    private String confirmReason;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "同行人信息")
    @Data
    public static class CompanionInfo {
        @Schema(description = "姓名", example = "张三")
        private String name;
        
        @Schema(description = "电话", example = "13800138000")
        private String phone;
        
        @Schema(description = "身份证号", example = "110101199001011234")
        private String idCard;
        
        @Schema(description = "照片URL", example = "https://example.com/photo.jpg")
        private String photo;
    }

}
