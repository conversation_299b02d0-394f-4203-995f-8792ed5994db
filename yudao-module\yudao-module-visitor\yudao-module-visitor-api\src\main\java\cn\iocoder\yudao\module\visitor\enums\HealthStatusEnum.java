package cn.iocoder.yudao.module.visitor.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 健康状态枚举
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Getter
@AllArgsConstructor
public enum HealthStatusEnum {

    NORMAL(1, "正常"),
    ABNORMAL(2, "异常");

    /**
     * 健康状态
     */
    private final Integer status;

    /**
     * 状态名称
     */
    private final String name;

    public static HealthStatusEnum valueOf(Integer status) {
        for (HealthStatusEnum value : values()) {
            if (value.getStatus().equals(status)) {
                return value;
            }
        }
        return null;
    }

}
