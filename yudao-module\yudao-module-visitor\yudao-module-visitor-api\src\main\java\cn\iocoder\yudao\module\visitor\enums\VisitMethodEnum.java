package cn.iocoder.yudao.module.visitor.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 到访方式枚举
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Getter
@AllArgsConstructor
public enum VisitMethodEnum{

    WITH_VEHICLE(1, "随车入园"),
    SEPARATE_ENTRY(2, "各自入园");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(VisitMethodEnum::getMethod).toArray();

    /**
     * 到访方式
     */
    private final Integer method;
    /**
     * 到访方式名称
     */
    private final String name;

    public static VisitMethodEnum valueOf(Integer method) {
        return Arrays.stream(values()).filter(item -> item.getMethod().equals(method)).findFirst().orElse(null);
    }

}
