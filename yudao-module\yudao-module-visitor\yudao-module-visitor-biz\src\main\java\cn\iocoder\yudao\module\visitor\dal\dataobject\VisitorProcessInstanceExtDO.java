package cn.iocoder.yudao.module.visitor.dal.dataobject;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 访客娴佺▼瀹炰緥鎵╁睍 DO
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@TableName(value = "visitor_process_instance_ext", autoResultMap = true)
@KeySequence("visitor_process_instance_ext_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VisitorProcessInstanceExtDO extends BaseDO {

    /**
     * 涓婚敭ID
     */
    @TableId
    private Long id;

    /**
     * 申请鍗旾D
     */
    private Long applicationId;

    /**
     * Flowable娴佺▼瀹炰緥ID
     */
    private String processInstanceId;

    /**
     * 娴佺▼瀹氫箟ID
     */
    private String processDefinitionId;

    /**
     * 娴佺▼瀹氫箟Key
     */
    private String processDefinitionKey;

    /**
     * 娴佺▼瀹氫箟名称
     */
    private String processDefinitionName;

    /**
     * 娴佺▼瀹氫箟鐗堟湰
     */
    private Integer processDefinitionVersion;

    /**
     * 涓氬姟Key锛堢敵璇峰崟鍙凤級
     */
    private String businessKey;

    /**
     * 娴佺▼瀹炰緥名称
     */
    private String processName;

    /**
     * 娴佺▼鍒嗙被
     */
    private String processCategory;

    /**
     * 鍙戣捣浜篒D
     */
    private Long startUserId;

    /**
     * 鍙戣捣浜哄鍚?     */
    private String startUserName;

    /**
     * 娴佺▼开始嬫椂闂?     */
    private LocalDateTime startTime;

    /**
     * 娴佺▼缁撴潫时间
     */
    private LocalDateTime endTime;

    /**
     * 娴佺▼鎸佺画时间锛堟绉掞級
     */
    private Long durationMillis;

    /**
     * 娴佺▼状态侊細1-杩愯涓?2-已完成?3-宸插彇娑?4-宸叉寕璧?5-异常缁堟
     */
    private Integer processStatus;

    /**
     * 褰撳墠娲诲姩鑺傜偣ID
     */
    private String currentActivityId;

    /**
     * 褰撳墠娲诲姩鑺傜偣名称
     */
    private String currentActivityName;

    /**
     * 褰撳墠澶勭悊浜篒D列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Long> currentAssigneeIds;

    /**
     * 娴佺▼鍙橀噺蹇収
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> processVariables;

    /**
     * 琛ㄥ崟鏁版嵁蹇収
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> formData;

    /**
     * 浼樺厛绾э細1-浣?2-涓?3-楂?     */
    private Integer priority;

    /**
     * 绱ф€ョ▼搴︼細1-绱ф€?2-鏅€?3-涓嶆€?     */
    private Integer urgencyLevel;

    /**
     * 棰勬湡完成时间
     */
    private LocalDateTime expectedCompletionTime;

    /**
     * 瀹為檯完成时间
     */
    private LocalDateTime actualCompletionTime;

    /**
     * SLA状态侊細1-正常 2-棰勮 3-瓒呮椂
     */
    private Integer slaStatus;

    /**
     * 鍗囩骇娆℃暟
     */
    private Integer escalationCount;

    /**
     * 最小鍚庡崌绾ф椂闂?     */
    private LocalDateTime lastEscalationTime;

    /**
     * 鎸傝捣鍘熷洜
     */
    private String suspensionReason;

    /**
     * 鎸傝捣时间
     */
    private LocalDateTime suspensionTime;

    /**
     * 鎭㈠时间
     */
    private LocalDateTime resumeTime;

    /**
     * 鍙栨秷鍘熷洜
     */
    private String cancellationReason;

    /**
     * 鍙栨秷时间
     */
    private LocalDateTime cancellationTime;

}

