package cn.iocoder.yudao.module.visitor.controller.admin.application.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 管理后台 - 访客申请联系人确认 Request VO
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Schema(description = "管理后台 - 访客申请联系人确认 Request VO")
@Data
public class VisitorApplicationConfirmReqVO {

    @Schema(description = "申请ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "申请ID不能为空")
    private Long id;

    @Schema(description = "是否确认", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    @NotNull(message = "确认结果不能为空")
    private Boolean confirmed;

    @Schema(description = "确认意见", example = "同意接待该访客")
    private String reason;

}
