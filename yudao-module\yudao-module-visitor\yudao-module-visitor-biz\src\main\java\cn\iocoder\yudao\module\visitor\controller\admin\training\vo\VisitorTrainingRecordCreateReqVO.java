package cn.iocoder.yudao.module.visitor.controller.admin.training.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 管理后台 - 访客培训完成记录创建 Request VO
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Schema(description = "管理后台 - 访客培训完成记录创建 Request VO")
@Data
public class VisitorTrainingRecordCreateReqVO {

    @Schema(description = "申请单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "申请单ID不能为空")
    private Long applicationId;

    @Schema(description = "培训ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "培训ID不能为空")
    private Long trainingId;

    @Schema(description = "访客姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotNull(message = "访客姓名不能为空")
    private String visitorName;

    @Schema(description = "开始嬫椂闂?, requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "开始嬫椂闂翠笉鑳戒负绌?)
    private LocalDateTime startTime;

    @Schema(description = "缁撴潫时间")
    private LocalDateTime endTime;

    @Schema(description = "瀹為檯培训时长锛堝垎閽燂級", example = "60")
    private Integer duration;

    @Schema(description = "完成状态?, example = "1")
    private Integer completionStatus;

    @Schema(description = "考试寰楀垎", example = "85")
    private Integer examScore;

    @Schema(description = "考试绛旀JSON")
    private List<Object> examAnswers;

    @Schema(description = "绛惧瓧鍥剧墖URL", example = "https://example.com/signature.jpg")
    private String signatureImage;

    @Schema(description = "璇佷功URL", example = "https://example.com/certificate.pdf")
    private String certificateUrl;

    @Schema(description = "IP地址", example = "*************")
    private String ipAddress;

    @Schema(description = "鐢ㄦ埛浠ｇ悊", example = "Mozilla/5.0...")
    private String userAgent;

    @Schema(description = "备注", example = "培训完成")
    private String remarks;

}

