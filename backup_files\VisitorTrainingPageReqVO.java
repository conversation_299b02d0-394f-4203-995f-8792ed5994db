package cn.iocoder.yudao.module.visitor.controller.admin.training.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 访客培训閰嶇疆分页 Request VO
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Schema(description = "管理后台 - 访客培训閰嶇疆分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VisitorTrainingPageReqVO extends PageParam {

    @Schema(description = "培训名称", example = "安全培训")
    private String trainingName;

    @Schema(description = "培训类型", example = "1")
    private Integer trainingType;

    @Schema(description = "閫傜敤访客类型", example = "1")
    private Integer visitorType;

    @Schema(description = "鏄惁必修", example = "1")
    private Integer isRequired;

    @Schema(description = "鏄惁鏈夎€冭瘯", example = "1")
    private Integer hasExam;

    @Schema(description = "状态?, example = "1")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}

