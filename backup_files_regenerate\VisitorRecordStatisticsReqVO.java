package cn.iocoder.yudao.module.visitor.controller.admin.record.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 访客进出统计 Request VO
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Schema(description = "管理后台 - 访客进出统计 Request VO")
@Data
public class VisitorRecordStatisticsReqVO {

    @Schema(description = "开始嬫椂闂?, requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "开始嬫椂闂翠笉鑳戒负绌?)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startTime;

    @Schema(description = "缁撴潫时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "缁撴潫时间不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endTime;

}

