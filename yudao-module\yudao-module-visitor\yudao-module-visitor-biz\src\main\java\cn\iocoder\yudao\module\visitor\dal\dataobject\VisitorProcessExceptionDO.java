package cn.iocoder.yudao.module.visitor.dal.dataobject;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 访客娴佺▼异常澶勭悊记录 DO
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@TableName(value = "visitor_process_exception", autoResultMap = true)
@KeySequence("visitor_process_exception_seq") // 鐢ㄤ簬 Oracle銆丳ostgreSQL銆並ingbase銆丏B2銆丠2 鏁版嵁搴撶殑涓婚敭鑷銆傚鏋滄槸 MySQL 绛夋暟鎹簱锛屽彲涓嶅啓
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VisitorProcessExceptionDO extends BaseDO {

    /**
     * 涓婚敭ID
     */
    @TableId
    private Long id;

    /**
     * 申请鍗旾D
     */
    private Long applicationId;

    /**
     * Flowable娴佺▼瀹炰緥ID
     */
    private String processInstanceId;

    /**
     * 异常浠诲姟ID
     */
    private String taskId;

    /**
     * 异常类型锛?-瓒呮椂鏈鐞?2-绯荤粺閿欒 3-鏁版嵁异常 4-鏉冮檺异常 5-涓氬姟瑙勫垯异常 6-澶栭儴鏈嶅姟异常 7-浜哄伐骞查 8-娴佺▼涓柇 9-鍏朵粬异常
     */
    private Integer exceptionType;

    /**
     * 异常绾у埆锛?-浣?2-涓?3-楂?4-绱ф€?     */
    private Integer exceptionLevel;

    /**
     * 异常浠ｇ爜
     */
    private String exceptionCode;

    /**
     * 异常鏍囬
     */
    private String exceptionTitle;

    /**
     * 异常璇︾粏描述
     */
    private String exceptionDescription;

    /**
     * 异常涓婁笅鏂囦俊鎭?     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> exceptionContext;

    /**
     * 异常鍫嗘爤信息
     */
    private String exceptionStackTrace;

    /**
     * 异常鍙戠敓时间
     */
    private LocalDateTime exceptionTime;

    /**
     * 鍙戠幇鏂瑰紡锛?-绯荤粺鑷姩检查娴?2-鐢ㄦ埛鍙嶉 3-鐩戞帶鍛婅 4-浜哄伐鍙戠幇
     */
    private Integer detectionMethod;

    /**
     * 鍙戠幇浜篒D
     */
    private Long detectorId;

    /**
     * 鍙戠幇浜哄鍚?     */
    private String detectorName;

    /**
     * 鍙楀奖鍝嶇敤鎴峰垪琛?     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> affectedUsers;

    /**
     * 涓氬姟褰卞搷描述
     */
    private String businessImpact;

    /**
     * 澶勭悊状态侊細1-寰呭鐞?2-澶勭悊涓?3-宸茶В鍐?4-宸插叧闂?5-鏃犻渶澶勭悊
     */
    private Integer handlingStatus;

    /**
     * 澶勭悊浜篒D
     */
    private Long handlerId;

    /**
     * 澶勭悊浜哄鍚?     */
    private String handlerName;

    /**
     * 开始嬪鐞嗘椂闂?     */
    private LocalDateTime handlingStartTime;

    /**
     * 澶勭悊完成时间
     */
    private LocalDateTime handlingEndTime;

    /**
     * 澶勭悊鑰楁椂锛堝垎閽燂級
     */
    private Integer handlingDuration;

    /**
     * 澶勭悊鏂瑰紡锛?-绯荤粺鑷姩淇 2-浜哄伐澶勭悊 3-娴佺▼閲嶅惎 4-鏁版嵁淇 5-閰嶇疆璋冩暣 6-鍏朵粬
     */
    private Integer handlingMethod;

    /**
     * 澶勭悊姝ラ记录
     */
    private String handlingSteps;

    /**
     * 澶勭悊缁撴灉描述
     */
    private String handlingResult;

    /**
     * 瑙ｅ喅鏂规类型锛?-涓存椂瑙ｅ喅 2-姘镐箙瑙ｅ喅 3-瑙勯伩鏂规 4-鏃犳硶瑙ｅ喅
     */
    private Integer resolutionType;

    /**
     * 棰勯槻鎺柦
     */
    private String preventionMeasures;

    /**
     * 鍏宠仈宸ュ崟鍙?     */
    private String relatedTicketNo;

    /**
     * 鍗囩骇绾у埆锛?-鏈崌绾?1-涓€绾у崌绾?2-浜岀骇鍗囩骇 3-涓夌骇鍗囩骇
     */
    private Integer escalationLevel;

    /**
     * 鍗囩骇时间
     */
    private LocalDateTime escalationTime;

    /**
     * 鍗囩骇鍘熷洜
     */
    private String escalationReason;

    /**
     * 鏄惁宸插彂閫侀€氱煡锛?-鍚?1-鏄?     */
    private Integer notificationSent;

    /**
     * 閫氱煡鎺ユ敹浜哄垪琛?     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> notificationRecipients;

    /**
     * 鏄惁闇€瑕佸悗缁窡杩涳細0-鍚?1-鏄?     */
    private Integer followUpRequired;

    /**
     * 璺熻繘时间
     */
    private LocalDateTime followUpTime;

    /**
     * 璺熻繘鍐呭
     */
    private String followUpContent;

    /**
     * 缁忛獙鏁欒鎬荤粨
     */
    private String lessonsLearned;

    /**
     * 鐩稿叧闄勪欢URL鏁扮粍
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> attachmentUrls;

}

