package cn.iocoder.yudao.module.visitor.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 培训类型枚举
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Getter
@AllArgsConstructor
public enum TrainingTypeEnum{

    SAFETY_TRAINING(1, "安全培训"),
    ENVIRONMENTAL_TRAINING(2, "环保培训"),
    QUALITY_TRAINING(3, "质量培训"),
    SECURITY_TRAINING(4, "保密培训"),
    EMERGENCY_TRAINING(5, "应急培训"),
    COMPREHENSIVE_TRAINING(6, "综合培训");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(TrainingTypeEnum::getType).toArray();

    /**
     * 培训类型
     */
    private final Integer type;
    /**
     * 培训类型名称
     */
    private final String name;

    public static TrainingTypeEnum valueOf(Integer type) {
        return Arrays.stream(values()).filter(item -> item.getType().equals(type)).findFirst().orElse(null);
    }

}
