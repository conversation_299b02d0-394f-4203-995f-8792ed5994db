package cn.iocoder.yudao.module.visitor.controller.admin.process.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 访客流程跟进记录分页 Request VO
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Schema(description = "管理后台 - 访客流程跟进记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VisitorProcessFollowPageReqVO extends PageParam {

    @Schema(description = "申请单ID", example = "1024")
    private Long applicationId;

    @Schema(description = "Flowable流程实例ID", example = "proc_inst_123")
    private String processInstanceId;

    @Schema(description = "跟进类型", example = "1")
    private Integer followType;

    @Schema(description = "跟进人ID", example = "1")
    private Long followPersonId;

    @Schema(description = "目标对象ID", example = "2")
    private Long targetPersonId;

    @Schema(description = "紧急程度", example = "2")
    private Integer urgencyLevel;

    @Schema(description = "是否已解决", example = "0")
    private Integer isResolved;

    @Schema(description = "跟进时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] followTime;

}
