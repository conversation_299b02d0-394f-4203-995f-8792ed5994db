package cn.iocoder.yudao.module.visitor.dal.dataobject;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 访客娴佺▼璺熻繘记录 DO
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@TableName(value = "visitor_process_follow", autoResultMap = true)
@KeySequence("visitor_process_follow_seq") // 鐢ㄤ簬 Oracle銆丳ostgreSQL銆並ingbase銆丏B2銆丠2 鏁版嵁搴撶殑涓婚敭鑷銆傚鏋滄槸 MySQL 绛夋暟鎹簱锛屽彲涓嶅啓
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VisitorProcessFollowDO extends BaseDO {

    /**
     * 涓婚敭ID
     */
    @TableId
    private Long id;

    /**
     * 申请鍗旾D
     */
    private Long applicationId;

    /**
     * Flowable娴佺▼瀹炰緥ID
     */
    private String processInstanceId;

    /**
     * 璺熻繘类型锛?-电话娌熼€?2-浼佷笟寰俊 3-閭欢娌熼€?4-鐜板満娌熼€?5-鐭俊閫氱煡 6-绯荤粺鎻愰啋 7-鍏朵粬
     */
    private Integer followType;

    /**
     * 娌熼€氭柟鍚戯細1-涓诲姩璺熻繘 2-琚姩鍝嶅簲
     */
    private Integer followDirection;

    /**
     * 璺熻繘浜篒D
     */
    private Long followPersonId;

    /**
     * 璺熻繘浜哄鍚?     */
    private String followPersonName;

    /**
     * 璺熻繘浜鸿鑹诧細1-申请浜?2-联系人?3-瀹℃壒浜?4-璀﹀崼 5-绯荤粺绠＄悊鍛?     */
    private Integer followPersonRole;

    /**
     * 鐩爣瀵硅薄ID
     */
    private Long targetPersonId;

    /**
     * 鐩爣瀵硅薄姓名
     */
    private String targetPersonName;

    /**
     * 鐩爣瀵硅薄瑙掕壊锛?-申请浜?2-联系人?3-瀹℃壒浜?4-璀﹀崼 5-绯荤粺绠＄悊鍛?     */
    private Integer targetPersonRole;

    /**
     * 璺熻繘时间
     */
    private LocalDateTime followTime;

    /**
     * 璺熻繘时长锛堝垎閽燂級
     */
    private Integer followDuration;

    /**
     * 璺熻繘涓婚
     */
    private String followSubject;

    /**
     * 璺熻繘鍐呭璇︽儏
     */
    private String followContent;

    /**
     * 璺熻繘缁撴灉锛?-闂瑙ｅ喅 2-闇€瑕佽繘涓€姝ヨ窡杩?3-杞氦浠栦汉澶勭悊 4-鏃犻渶澶勭悊
     */
    private Integer followResult;

    /**
     * 涓嬫璺熻繘时间
     */
    private LocalDateTime nextFollowTime;

    /**
     * 涓嬫璺熻繘浜篒D
     */
    private Long nextFollowPersonId;

    /**
     * 闄勪欢URL鏁扮粍锛堝綍闊炽€佹埅鍥剧瓑锛?     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> attachmentUrls;

    /**
     * 鍏宠仈鐨勪换鍔D
     */
    private String relatedTaskId;

    /**
     * 绱ф€ョ▼搴︼細1-绱ф€?2-鏅€?3-涓嶆€?     */
    private Integer urgencyLevel;

    /**
     * 鏄惁宸茶В鍐筹細0-鏈В鍐?1-宸茶В鍐?     */
    private Integer isResolved;

    /**
     * 瑙ｅ喅时间
     */
    private LocalDateTime resolutionTime;

    /**
     * 婊℃剰搴﹁瘎鍒嗭紙1-5鍒嗭級
     */
    private Integer satisfactionScore;

}

