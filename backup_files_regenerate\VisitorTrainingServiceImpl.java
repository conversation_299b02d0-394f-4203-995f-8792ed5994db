package cn.iocoder.yudao.module.visitor.service.training;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.visitor.controller.admin.training.vo.VisitorTrainingCreateReqVO;
import cn.iocoder.yudao.module.visitor.controller.admin.training.vo.VisitorTrainingPageReqVO;
import cn.iocoder.yudao.module.visitor.controller.admin.training.vo.VisitorTrainingUpdateReqVO;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorTrainingDO;
import cn.iocoder.yudao.module.visitor.dal.mysql.VisitorTrainingMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.visitor.enums.ErrorCodeConstants.VISITOR_TRAINING_NOT_EXISTS;

/**
 * 访客培训 Service 实现类? *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Service
@Validated
@Slf4j
public class VisitorTrainingServiceImpl implements VisitorTrainingService {

    @Resource
    private VisitorTrainingMapper visitorTrainingMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createVisitorTraining(@Valid VisitorTrainingCreateReqVO createReqVO) {
        log.info("[createVisitorTraining] 创建访客培训锛屽弬鏁帮細{}", createReqVO);
        
        // 鎻掑叆
        VisitorTrainingDO visitorTraining = BeanUtils.toBean(createReqVO, VisitorTrainingDO.class);
        visitorTrainingMapper.insert(visitorTraining);
        
        log.info("[createVisitorTraining] 创建访客培训成功锛孖D锛歿}", visitorTraining.getId());
        return visitorTraining.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateVisitorTraining(@Valid VisitorTrainingUpdateReqVO updateReqVO) {
        log.info("[updateVisitorTraining] 更新访客培训锛屽弬鏁帮細{}", updateReqVO);
        
        // 校验存在
        validateVisitorTrainingExists(updateReqVO.getId());
        
        // 更新
        VisitorTrainingDO updateObj = BeanUtils.toBean(updateReqVO, VisitorTrainingDO.class);
        visitorTrainingMapper.updateById(updateObj);
        
        log.info("[updateVisitorTraining] 更新访客培训成功锛孖D锛歿}", updateReqVO.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteVisitorTraining(Long id) {
        log.info("[deleteVisitorTraining] 删除访客培训锛孖D锛歿}", id);
        
        // 校验存在
        validateVisitorTrainingExists(id);
        
        // 删除
        visitorTrainingMapper.deleteById(id);
        
        log.info("[deleteVisitorTraining] 删除访客培训成功锛孖D锛歿}", id);
    }

    private void validateVisitorTrainingExists(Long id) {
        if (visitorTrainingMapper.selectById(id) == null) {
            throw exception(VISITOR_TRAINING_NOT_EXISTS);
        }
    }

    @Override
    public VisitorTrainingDO getVisitorTraining(Long id) {
        return visitorTrainingMapper.selectById(id);
    }

    @Override
    public List<VisitorTrainingDO> getVisitorTrainingList(Collection<Long> ids) {
        return visitorTrainingMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<VisitorTrainingDO> getVisitorTrainingPage(VisitorTrainingPageReqVO pageReqVO) {
        return visitorTrainingMapper.selectPage(pageReqVO);
    }

    @Override
    public List<VisitorTrainingDO> getVisitorTrainingList(VisitorTrainingPageReqVO exportReqVO) {
        return visitorTrainingMapper.selectList(exportReqVO);
    }

    @Override
    public List<VisitorTrainingDO> getTrainingByVisitorType(Integer visitorType) {
        return visitorTrainingMapper.selectListByVisitorType(visitorType);
    }

    @Override
    public List<VisitorTrainingDO> getTrainingByType(Integer trainingType) {
        return visitorTrainingMapper.selectListByTrainingType(trainingType);
    }

    @Override
    public List<VisitorTrainingDO> getRequiredTrainingByVisitorType(Integer visitorType) {
        return visitorTrainingMapper.selectRequiredByVisitorType(visitorType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTrainingStatus(Long id, Integer status) {
        log.info("[updateTrainingStatus] 更新培训状态侊紝ID锛歿}锛岀姸鎬侊細{}", id, status);
        
        // 校验存在
        validateVisitorTrainingExists(id);
        
        // 更新状态?        VisitorTrainingDO updateObj = new VisitorTrainingDO();
        updateObj.setId(id);
        updateObj.setStatus(status);
        visitorTrainingMapper.updateById(updateObj);
        
        log.info("[updateTrainingStatus] 培训状态佹洿鏂版垚鍔燂紝ID锛歿}锛屾柊状态侊細{}", id, status);
    }

}

