package cn.iocoder.yudao.module.visitor.controller.admin.record;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.module.visitor.controller.admin.record.vo.*;
import cn.iocoder.yudao.module.visitor.convert.record.VisitorRecordConvert;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorRecordDO;
import cn.iocoder.yudao.module.visitor.service.record.VisitorRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

/**
 * 管理后台 - 访客进出记录 Controller
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Tag(name = "管理后台 - 访客进出记录")
@RestController
@RequestMapping("/visitor/record")
@Validated
@Slf4j
public class VisitorRecordController {

    @Resource
    private VisitorRecordService visitorRecordService;

    @PostMapping("/create")
    @Operation(summary = "创建访客进出记录")
    @PreAuthorize("@ss.hasPermission('visitor:record:create')")
    public CommonResult<Long> createVisitorRecord(@Valid @RequestBody VisitorRecordCreateReqVO createReqVO) {
        return success(visitorRecordService.createVisitorRecord(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新访客进出记录")
    @PreAuthorize("@ss.hasPermission('visitor:record:update')")
    public CommonResult<Boolean> updateVisitorRecord(@Valid @RequestBody VisitorRecordUpdateReqVO updateReqVO) {
        visitorRecordService.updateVisitorRecord(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除访客进出记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('visitor:record:delete')")
    public CommonResult<Boolean> deleteVisitorRecord(@RequestParam("id") Long id) {
        visitorRecordService.deleteVisitorRecord(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得访客进出记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('visitor:record:query')")
    public CommonResult<VisitorRecordRespVO> getVisitorRecord(@RequestParam("id") Long id) {
        VisitorRecordDO visitorRecord = visitorRecordService.getVisitorRecord(id);
        return success(BeanUtils.toBean(visitorRecord, VisitorRecordRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得访客进出记录分页")
    @PreAuthorize("@ss.hasPermission('visitor:record:query')")
    public CommonResult<PageResult<VisitorRecordRespVO>> getVisitorRecordPage(@Valid VisitorRecordPageReqVO pageReqVO) {
        PageResult<VisitorRecordDO> pageResult = visitorRecordService.getVisitorRecordPage(pageReqVO);
        return success(VisitorRecordConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出访客进出记录Excel")
    @PreAuthorize("@ss.hasPermission('visitor:record:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportVisitorRecordExcel(@Valid VisitorRecordPageReqVO pageReqVO,
                                        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<VisitorRecordDO> list = visitorRecordService.getVisitorRecordList(pageReqVO);
        ExcelUtils.write(response, "访客进出记录.xls", "鏁版嵁", VisitorRecordRespVO.class,
                VisitorRecordConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/statistics")
    @Operation(summary = "鑾峰彇访客进出统计")
    @PreAuthorize("@ss.hasPermission('visitor:record:query')")
    public CommonResult<Object> getVisitorRecordStatistics(@Valid VisitorRecordStatisticsReqVO reqVO) {
        Object statistics = visitorRecordService.getVisitorRecordStatistics(reqVO.getStartTime(), reqVO.getEndTime());
        return success(statistics);
    }

    @GetMapping("/current-count")
    @Operation(summary = "鑾峰彇褰撳墠鍦ㄥ洯访客鏁伴噺")
    @PreAuthorize("@ss.hasPermission('visitor:record:query')")
    public CommonResult<Long> getCurrentVisitorCount() {
        Long count = visitorRecordService.getCurrentVisitorCount();
        return success(count);
    }

}

