package cn.iocoder.yudao.module.visitor.controller.app.training.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 鐢ㄦ埛 APP - 培训进度 Response VO
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Schema(description = "鐢ㄦ埛 APP - 培训进度 Response VO")
@Data
public class AppTrainingProgressRespVO {

    @Schema(description = "鎬诲煿璁暟閲?, example = "5")
    private Long totalTrainings;

    @Schema(description = "已完成愬煿璁暟閲?, example = "3")
    private Long completedTrainings;

    @Schema(description = "进度鐧惧垎姣?, example = "60.0")
    private Double progressPercentage;

    @Schema(description = "鏄惁鍏ㄩ儴完成", example = "false")
    private Boolean allCompleted;

}

