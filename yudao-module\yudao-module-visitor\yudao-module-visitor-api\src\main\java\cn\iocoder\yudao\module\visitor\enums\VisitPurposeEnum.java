package cn.iocoder.yudao.module.visitor.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 访问目的枚举
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Getter
@AllArgsConstructor
public enum VisitPurposeEnum{

    VISIT(1, "参观"),
    BUSINESS(2, "商务"),
    CONSTRUCTION(3, "施工"),
    INSPECTION(4, "检查");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(VisitPurposeEnum::getPurpose).toArray();

    /**
     * 访问目的
     */
    private final Integer purpose;
    /**
     * 访问目的名称
     */
    private final String name;

    public static VisitPurposeEnum valueOf(Integer purpose) {
        return Arrays.stream(values()).filter(item -> item.getPurpose().equals(purpose)).findFirst().orElse(null);
    }

}
