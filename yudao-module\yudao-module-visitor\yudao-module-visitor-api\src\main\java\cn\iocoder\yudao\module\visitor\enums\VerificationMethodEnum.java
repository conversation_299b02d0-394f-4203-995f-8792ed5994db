package cn.iocoder.yudao.module.visitor.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 验证方式枚举
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Getter
@AllArgsConstructor
public enum VerificationMethodEnum{

    QR_CODE(1, "二维码"),
    MANUAL(2, "手动"),
    FACE_RECOGNITION(3, "人脸识别");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(VerificationMethodEnum::getMethod).toArray();

    /**
     * 验证方式
     */
    private final Integer method;
    /**
     * 验证方式名称
     */
    private final String name;

    public static VerificationMethodEnum valueOf(Integer method) {
        return Arrays.stream(values()).filter(item -> item.getMethod().equals(method)).findFirst().orElse(null);
    }

}
