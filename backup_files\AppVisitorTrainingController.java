package cn.iocoder.yudao.module.visitor.controller.app.training;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import cn.iocoder.yudao.module.visitor.controller.app.training.vo.AppTrainingCompleteReqVO;
import cn.iocoder.yudao.module.visitor.controller.app.training.vo.AppTrainingProgressRespVO;
import cn.iocoder.yudao.module.visitor.controller.app.training.vo.AppTrainingRespVO;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorTrainingDO;
import cn.iocoder.yudao.module.visitor.service.training.VisitorTrainingRecordService;
import cn.iocoder.yudao.module.visitor.service.training.VisitorTrainingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 鐢ㄦ埛 APP - 访客培训 Controller
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Tag(name = "鐢ㄦ埛 APP - 访客培训")
@RestController
@RequestMapping("/app-api/visitor/training")
@Validated
@Slf4j
public class AppVisitorTrainingController {

    @Resource
    private VisitorTrainingService visitorTrainingService;

    @Resource
    private VisitorTrainingRecordService visitorTrainingRecordService;

    @GetMapping("/list")
    @Operation(summary = "鑾峰彇培训列表")
    @Parameter(name = "visitorType", description = "访客类型", required = true)
    @PreAuthorize("hasRole('ROLE_USER')")
    public CommonResult<List<AppTrainingRespVO>> getTrainingList(@RequestParam("visitorType") Integer visitorType) {
        List<VisitorTrainingDO> list = visitorTrainingService.getTrainingByVisitorType(visitorType);
        return success(BeanUtils.toBean(list, AppTrainingRespVO.class));
    }

    @GetMapping("/get")
    @Operation(summary = "鑾峰彇培训璇︽儏")
    @Parameter(name = "id", description = "培训ID", required = true)
    @PreAuthorize("hasRole('ROLE_USER')")
    public CommonResult<AppTrainingRespVO> getTraining(@RequestParam("id") Long id) {
        VisitorTrainingDO training = visitorTrainingService.getVisitorTraining(id);
        return success(BeanUtils.toBean(training, AppTrainingRespVO.class));
    }

    @PostMapping("/start")
    @Operation(summary = "开始嬪煿璁?)
    @PreAuthorize("hasRole('ROLE_USER')")
    public CommonResult<Long> startTraining(@RequestParam("applicationId") Long applicationId,
                                           @RequestParam("trainingId") Long trainingId,
                                           @RequestParam("visitorName") String visitorName) {
        Long recordId = visitorTrainingRecordService.startTraining(applicationId, trainingId, visitorName);
        return success(recordId);
    }

    @PostMapping("/complete")
    @Operation(summary = "完成培训")
    @PreAuthorize("hasRole('ROLE_USER')")
    public CommonResult<Boolean> completeTraining(@Valid @RequestBody AppTrainingCompleteReqVO reqVO) {
        visitorTrainingRecordService.completeTraining(reqVO.getRecordId(), reqVO.getExamScore(),
                reqVO.getExamAnswers(), reqVO.getSignatureImage());
        return success(true);
    }

    @GetMapping("/progress")
    @Operation(summary = "鑾峰彇培训进度")
    @Parameter(name = "applicationId", description = "申请ID", required = true)
    @PreAuthorize("hasRole('ROLE_USER')")
    public CommonResult<AppTrainingProgressRespVO> getTrainingProgress(@RequestParam("applicationId") Long applicationId) {
        Object progress = visitorTrainingRecordService.getTrainingProgress(applicationId);
        return success(BeanUtils.toBean(progress, AppTrainingProgressRespVO.class));
    }

    @GetMapping("/check-completion")
    @Operation(summary = "检查ュ煿璁畬鎴愭儏鍐?)
    @PreAuthorize("hasRole('ROLE_USER')")
    public CommonResult<Boolean> checkTrainingCompletion(@RequestParam("applicationId") Long applicationId,
                                                         @RequestParam("visitorType") Integer visitorType) {
        boolean completed = visitorTrainingRecordService.isAllRequiredTrainingCompleted(applicationId, visitorType);
        return success(completed);
    }

}

