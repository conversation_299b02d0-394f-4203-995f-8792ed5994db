package cn.iocoder.yudao.module.visitor.controller.app.training.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 鐢ㄦ埛 APP - 培训信息 Response VO
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Schema(description = "鐢ㄦ埛 APP - 培训信息 Response VO")
@Data
public class AppTrainingRespVO {

    @Schema(description = "培训ID", example = "1024")
    private Long id;

    @Schema(description = "培训名称", example = "安全培训")
    private String trainingName;

    @Schema(description = "培训类型", example = "1")
    private Integer trainingType;

    @Schema(description = "培训鍐呭", example = "安全操作员瑙勭▼...")
    private String trainingContent;

    @Schema(description = "培训瑙嗛URL", example = "https://example.com/video.mp4")
    private String trainingVideoUrl;

    @Schema(description = "培训鏂囨。URL", example = "https://example.com/doc.pdf")
    private String trainingDocumentUrl;

    @Schema(description = "最小忓煿璁椂闀匡紙分钟锛?, example = "30")
    private Integer minDuration;

    @Schema(description = "鏄惁鏈夎€冭瘯", example = "true")
    private Boolean hasExam;

    @Schema(description = "考试棰樼洰", example = "[{\"question\":\"...\",\"options\":[...]}]")
    private String examQuestions;

    @Schema(description = "鍙婃牸分数", example = "80")
    private Integer passScore;

    @Schema(description = "鏄惁必修", example = "true")
    private Boolean isRequired;

    @Schema(description = "鎺掑簭", example = "1")
    private Integer sortOrder;

}

