package cn.iocoder.yudao.module.visitor.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * 访客管理错误码枚举类
 *
 * 访客管理系统，使用 1-003-000-000 段
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
public interface ErrorCodeConstants {

    // ========== 访客申请相关 1-003-001-000 ==========
    ErrorCode VISITOR_APPLICATION_NOT_EXISTS = new ErrorCode(1_003_001_000, "访客申请不存在");
    ErrorCode VISITOR_APPLICATION_STATUS_NOT_ALLOW_UPDATE = new ErrorCode(1_003_001_001, "访客申请状态不允许修改");
    ErrorCode VISITOR_APPLICATION_ALREADY_APPROVED = new ErrorCode(1_003_001_002, "访客申请已审批");
    ErrorCode VISITOR_APPLICATION_ALREADY_REJECTED = new ErrorCode(1_003_001_003, "访客申请已驳回");
    ErrorCode VISITOR_APPLICATION_NOT_APPROVED = new ErrorCode(1_003_001_004, "访客申请未审批");
    ErrorCode VISITOR_APPLICATION_ALREADY_ENTERED = new ErrorCode(1_003_001_005, "访客已入园");
    ErrorCode VISITOR_APPLICATION_ALREADY_EXITED = new ErrorCode(1_003_001_006, "访客已出园");
    ErrorCode VISITOR_APPLICATION_EXPIRED = new ErrorCode(1_003_001_007, "访客申请已过期");
    ErrorCode VISITOR_APPLICATION_TRAINING_NOT_COMPLETED = new ErrorCode(1_003_001_008, "访客培训未完成");

    // ========== 访客记录相关 1-003-002-000 ==========
    ErrorCode VISITOR_RECORD_NOT_EXISTS = new ErrorCode(1_003_002_000, "访客记录不存在");

    // ========== 访客培训相关 1-003-003-000 ==========
    ErrorCode VISITOR_TRAINING_NOT_EXISTS = new ErrorCode(1_003_003_000, "访客培训不存在");
    ErrorCode VISITOR_TRAINING_RECORD_NOT_EXISTS = new ErrorCode(1_003_003_001, "访客培训记录不存在");
    ErrorCode VISITOR_TRAINING_ALREADY_COMPLETED = new ErrorCode(1_003_003_002, "访客培训已完成");
    ErrorCode VISITOR_TRAINING_NOT_REQUIRED = new ErrorCode(1_003_003_003, "访客培训非必修");

    // ========== 二维码相关 1-003-004-000 ==========
    ErrorCode VISITOR_QR_CODE_INVALID = new ErrorCode(1_003_004_000, "访客二维码无效");
    ErrorCode VISITOR_QR_CODE_EXPIRED = new ErrorCode(1_003_004_001, "访客二维码已过期");
    ErrorCode VISITOR_QR_CODE_ALREADY_USED = new ErrorCode(1_003_004_002, "访客二维码已使用");

    // ========== 流程相关 1-003-005-000 ==========
    ErrorCode VISITOR_PROCESS_NOT_EXISTS = new ErrorCode(1_003_005_000, "访客流程不存在");
    ErrorCode VISITOR_PROCESS_ALREADY_STARTED = new ErrorCode(1_003_005_001, "访客流程已启动");
    ErrorCode VISITOR_PROCESS_NOT_STARTED = new ErrorCode(1_003_005_002, "访客流程未启动");

}
