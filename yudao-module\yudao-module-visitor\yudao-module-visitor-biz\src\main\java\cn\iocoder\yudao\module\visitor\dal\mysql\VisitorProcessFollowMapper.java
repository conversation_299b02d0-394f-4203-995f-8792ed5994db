package cn.iocoder.yudao.module.visitor.dal.mysql;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.visitor.controller.admin.process.vo.VisitorProcessFollowPageReqVO;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorProcessFollowDO;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 访客娴佺▼璺熻繘记录 Mapper
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Mapper
public interface VisitorProcessFollowMapper extends BaseMapperX<VisitorProcessFollowDO> {

    default PageResult<VisitorProcessFollowDO> selectPage(VisitorProcessFollowPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<VisitorProcessFollowDO>()
                .eqIfPresent(VisitorProcessFollowDO::getApplicationId, reqVO.getApplicationId())
                .eqIfPresent(VisitorProcessFollowDO::getProcessInstanceId, reqVO.getProcessInstanceId())
                .eqIfPresent(VisitorProcessFollowDO::getFollowType, reqVO.getFollowType())
                .eqIfPresent(VisitorProcessFollowDO::getFollowPersonId, reqVO.getFollowPersonId())
                .eqIfPresent(VisitorProcessFollowDO::getTargetPersonId, reqVO.getTargetPersonId())
                .eqIfPresent(VisitorProcessFollowDO::getUrgencyLevel, reqVO.getUrgencyLevel())
                .eqIfPresent(VisitorProcessFollowDO::getIsResolved, reqVO.getIsResolved())
                .betweenIfPresent(VisitorProcessFollowDO::getFollowTime, reqVO.getFollowTime())
                .orderByDesc(VisitorProcessFollowDO::getId));
    }

    default List<VisitorProcessFollowDO> selectListByApplicationId(Long applicationId) {
        return selectList(new LambdaQueryWrapperX<VisitorProcessFollowDO>()
                .eq(VisitorProcessFollowDO::getApplicationId, applicationId)
                .orderByDesc(VisitorProcessFollowDO::getFollowTime));
    }

    default List<VisitorProcessFollowDO> selectPendingFollowUps(LocalDateTime currentTime) {
        return selectList(new LambdaQueryWrapperX<VisitorProcessFollowDO>()
                .eq(VisitorProcessFollowDO::getIsResolved, 0)
                .le(VisitorProcessFollowDO::getNextFollowTime, currentTime)
                .isNotNull(VisitorProcessFollowDO::getNextFollowTime));
    }

}

