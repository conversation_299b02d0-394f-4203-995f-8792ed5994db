package cn.iocoder.yudao.module.visitor.dal.mysql;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorProcessInstanceExtDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 访客娴佺▼瀹炰緥鎵╁睍 Mapper
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Mapper
public interface VisitorProcessInstanceExtMapper extends BaseMapperX<VisitorProcessInstanceExtDO> {

    /**
     * 鏍规嵁申请ID查询娴佺▼瀹炰緥鎵╁睍信息
     *
     * @param applicationId 申请ID
     * @return 娴佺▼瀹炰緥鎵╁睍信息
     */
    default VisitorProcessInstanceExtDO selectByApplicationId(Long applicationId) {
        return selectOne(VisitorProcessInstanceExtDO::getApplicationId, applicationId);
    }

    /**
     * 鏍规嵁娴佺▼瀹炰緥ID查询娴佺▼瀹炰緥鎵╁睍信息
     *
     * @param processInstanceId 娴佺▼瀹炰緥ID
     * @return 娴佺▼瀹炰緥鎵╁睍信息
     */
    default VisitorProcessInstanceExtDO selectByProcessInstanceId(String processInstanceId) {
        return selectOne(VisitorProcessInstanceExtDO::getProcessInstanceId, processInstanceId);
    }

    /**
     * 鏍规嵁涓氬姟Key查询娴佺▼瀹炰緥鎵╁睍信息
     *
     * @param businessKey 涓氬姟Key
     * @return 娴佺▼瀹炰緥鎵╁睍信息
     */
    default VisitorProcessInstanceExtDO selectByBusinessKey(String businessKey) {
        return selectOne(VisitorProcessInstanceExtDO::getBusinessKey, businessKey);
    }

}

