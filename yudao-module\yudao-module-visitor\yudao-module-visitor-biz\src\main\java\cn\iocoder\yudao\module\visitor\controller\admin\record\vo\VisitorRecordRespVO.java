package cn.iocoder.yudao.module.visitor.controller.admin.record.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 管理后台 - 访客进出记录 Response VO
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Schema(description = "管理后台 - 访客进出记录 Response VO")
@Data
public class VisitorRecordRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "申请单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long applicationId;

    @Schema(description = "访客姓名", example = "张三")
    private String visitorName;

    @Schema(description = "访客电话", example = "13800138000")
    private String visitorPhone;

    @Schema(description = "操作类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer operationType;

    @Schema(description = "操作时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime operationTime;

    @Schema(description = "现场访客照片URL", example = "https://example.com/visitor.jpg")
    private String visitorPhoto;

    @Schema(description = "操作员ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long operatorId;

    @Schema(description = "操作员姓名", example = "管理员")
    private String operatorName;

    @Schema(description = "门岗位置", requiredMode = Schema.RequiredMode.REQUIRED, example = "东门")
    private String gateLocation;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}
