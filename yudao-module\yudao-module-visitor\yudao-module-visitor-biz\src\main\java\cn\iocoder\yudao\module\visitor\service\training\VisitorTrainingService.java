package cn.iocoder.yudao.module.visitor.service.training;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.visitor.controller.admin.training.vo.VisitorTrainingCreateReqVO;
import cn.iocoder.yudao.module.visitor.controller.admin.training.vo.VisitorTrainingPageReqVO;
import cn.iocoder.yudao.module.visitor.controller.admin.training.vo.VisitorTrainingUpdateReqVO;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorTrainingDO;

import jakarta.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 访客培训 Service 接口
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
public interface VisitorTrainingService {

    /**
     * 创建访客培训
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createVisitorTraining(@Valid VisitorTrainingCreateReqVO createReqVO);

    /**
     * 更新访客培训
     *
     * @param updateReqVO 更新信息
     */
    void updateVisitorTraining(@Valid VisitorTrainingUpdateReqVO updateReqVO);

    /**
     * 删除访客培训
     *
     * @param id 编号
     */
    void deleteVisitorTraining(Long id);

    /**
     * 获得访客培训
     *
     * @param id 编号
     * @return 访客培训
     */
    VisitorTrainingDO getVisitorTraining(Long id);

    /**
     * 获得访客培训列表
     *
     * @param ids 编号
     * @return 访客培训列表
     */
    List<VisitorTrainingDO> getVisitorTrainingList(Collection<Long> ids);

    /**
     * 获得访客培训分页
     *
     * @param pageReqVO 分页查询
     * @return 访客培训分页
     */
    PageResult<VisitorTrainingDO> getVisitorTrainingPage(VisitorTrainingPageReqVO pageReqVO);

    /**
     * 获得访客培训列表, 鐢ㄤ簬 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 访客培训列表
     */
    List<VisitorTrainingDO> getVisitorTrainingList(VisitorTrainingPageReqVO exportReqVO);

    /**
     * 鏍规嵁访客类型鑾峰彇培训列表
     *
     * @param visitorType 访客类型
     * @return 培训列表
     */
    List<VisitorTrainingDO> getTrainingByVisitorType(Integer visitorType);

    /**
     * 鏍规嵁培训类型鑾峰彇培训列表
     *
     * @param trainingType 培训类型
     * @return 培训列表
     */
    List<VisitorTrainingDO> getTrainingByType(Integer trainingType);

    /**
     * 鑾峰彇必修培训列表
     *
     * @param visitorType 访客类型
     * @return 必修培训列表
     */
    List<VisitorTrainingDO> getRequiredTrainingByVisitorType(Integer visitorType);

    /**
     * 鍚敤/禁用培训
     *
     * @param id 培训ID
     * @param status 状态?     */
    void updateTrainingStatus(Long id, Integer status);

}

