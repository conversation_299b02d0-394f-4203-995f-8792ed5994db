package cn.iocoder.yudao.module.visitor.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 访客类型枚举
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Getter
@AllArgsConstructor
public enum VisitorTypeEnum{

    NORMAL(1, "普通访客"),
    GOVERNMENT(2, "政府访客"),
    CONTRACTOR(3, "施工承包商");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(VisitorTypeEnum::getType).toArray();

    /**
     * 访客类型
     */
    private final Integer type;
    /**
     * 访客类型名称
     */
    private final String name;

    public static VisitorTypeEnum valueOf(Integer type) {
        return Arrays.stream(values()).filter(item -> item.getType().equals(type)).findFirst().orElse(null);
    }

}
