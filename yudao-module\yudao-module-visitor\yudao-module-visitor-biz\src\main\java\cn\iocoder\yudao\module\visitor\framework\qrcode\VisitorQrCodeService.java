﻿package cn.iocoder.yudao.module.visitor.framework.qrcode;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorApplicationDO;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

/**
 * 访客二维码服务? *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Service
@Slf4j
public class VisitorQrCodeService {

    @Value("${visitor.qrcode.secret:visitor_qrcode_secret_key_2025}")
    private String qrCodeSecret;

    @Value("${visitor.qrcode.expire-hours:24}")
    private Integer expireHours;

    /**
     * 鐢熸垚访客二维码?     *
     * @param application 访客申请
     * @return 二维码丅ase64瀛楃涓?     */
    public String generateQrCode(VisitorApplicationDO application) {
        try {
            // 1. 鐢熸垚JWT Token
            String token = generateJwtToken(application);

            // 2. 鐢熸垚二维码佸浘鐗?            BufferedImage qrCodeImage = generateQrCodeImage(token, application);

            // 3. 杞崲涓築ase64瀛楃涓?            return imageToBase64(qrCodeImage);
            
        } catch (Exception e) {
            log.error("[generateQrCode] 鐢熸垚二维码佸け璐ワ紝申请ID锛歿}", application.getId(), e);
            throw new RuntimeException("二维码佺敓鎴愬け璐?, e);
        }
    }

    /**
     * 验证二维码?     *
     * @param qrCodeContent 二维码佸唴瀹?     * @return 验证缁撴灉
     */
    public QrCodeValidationResult validateQrCode(String qrCodeContent) {
        try {
            // 瑙ｆ瀽JWT Token
            SecretKey key = new SecretKeySpec(qrCodeSecret.getBytes(), "HmacSHA256");
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(key)
                    .build()
                    .parseClaimsJws(qrCodeContent)
                    .getBody();

            // 检查查询繃鏈熸椂闂?            Instant expiration = claims.getExpiration().toInstant();
            if (expiration.isBefore(Instant.now())) {
                return QrCodeValidationResult.expired();
            }

            // 鎻愬彇访客信息
            Long applicationId = claims.get("applicationId", Long.class);
            String visitorName = claims.get("visitorName", String.class);
            String visitorPhone = claims.get("visitorPhone", String.class);
            String visitArea = claims.get("visitArea", String.class);

            return QrCodeValidationResult.success(applicationId, visitorName, visitorPhone, visitArea);

        } catch (Exception e) {
            log.error("[validateQrCode] 二维码侀獙璇佸け璐ワ紝鍐呭锛歿}", qrCodeContent, e);
            return QrCodeValidationResult.invalid();
        }
    }

    /**
     * 鐢熸垚JWT Token
     */
    private String generateJwtToken(VisitorApplicationDO application) {
        Instant now = Instant.now();
        Instant expiration = LocalDateTime.now().plusHours(expireHours)
                .atZone(ZoneId.systemDefault()).toInstant();

        return Jwts.builder()
                .setSubject("visitor_access")
                .setIssuedAt(java.util.Date.from(now))
                .setExpiration(java.util.Date.from(expiration))
                .claim("applicationId", application.getId())
                .claim("applicationNo", application.getApplicationNo())
                .claim("visitorName", application.getVisitorName())
                .claim("visitorPhone", application.getVisitorPhone())
                .claim("companyName", application.getCompanyName())
                .claim("visitArea", application.getVisitArea())
                .claim("visitStartTime", application.getVisitStartTime().toString())
                .claim("visitEndTime", application.getVisitEndTime().toString())
                .claim("hasVehicle", application.getHasVehicle())
                .claim("vehiclePlate", application.getVehiclePlate())
                .claim("nonce", IdUtil.getSnowflakeNextIdStr())
                .signWith(new SecretKeySpec(qrCodeSecret.getBytes(), "HmacSHA256"))
                .compact();
    }

    /**
     * 鐢熸垚二维码佸浘鐗?     */
    private BufferedImage generateQrCodeImage(String content, VisitorApplicationDO application) throws WriterException {
        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        hints.put(EncodeHintType.MARGIN, 1);

        QRCodeWriter qrCodeWriter = new QRCodeWriter();
        BitMatrix bitMatrix = qrCodeWriter.encode(content, BarcodeFormat.QR_CODE, 300, 300, hints);

        BufferedImage qrCodeImage = MatrixToImageWriter.toBufferedImage(bitMatrix);

        // 娣诲姞访客信息鏂囧瓧
        return addVisitorInfoToImage(qrCodeImage, application);
    }

    /**
     * 鍦ㄤ簩缁寸爜鍥剧墖涓婃坊鍔犺瀹俊鎭?     */
    private BufferedImage addVisitorInfoToImage(BufferedImage qrCodeImage, VisitorApplicationDO application) {
        int width = qrCodeImage.getWidth();
        int height = qrCodeImage.getHeight() + 80; // 澧炲姞楂樺害鐢ㄤ簬鏄剧ず鏂囧瓧

        BufferedImage combinedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = combinedImage.createGraphics();

        // 璁剧疆鑳屾櫙鑹?        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, width, height);

        // 缁樺埗二维码?        g2d.drawImage(qrCodeImage, 0, 0, null);

        // 璁剧疆瀛椾綋鍜岄鑹?        g2d.setColor(Color.BLACK);
        g2d.setFont(new Font("SimSun", Font.BOLD, 12));

        // 娣诲姞访客信息鏂囧瓧
        int textY = qrCodeImage.getHeight() + 15;
        g2d.drawString("访客锛? + application.getVisitorName(), 10, textY);
        g2d.drawString("鍗曚綅锛? + application.getCompanyName(), 10, textY + 20);
        g2d.drawString("鏈夋晥鏈燂細" + DateUtil.format(application.getVisitEndTime(), "MM-dd HH:mm"), 10, textY + 40);
        g2d.drawString("申请鍙凤細" + application.getApplicationNo(), 10, textY + 60);

        g2d.dispose();
        return combinedImage;
    }

    /**
     * 鍥剧墖杞珺ase64
     */
    private String imageToBase64(BufferedImage image) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        javax.imageio.ImageIO.write(image, "PNG", baos);
        byte[] imageBytes = baos.toByteArray();
        return "data:image/png;base64," + Base64.encode(imageBytes);
    }

    /**
     * 二维码侀獙璇佺粨鏋?     */
    public static class QrCodeValidationResult {
        private boolean valid;
        private boolean expired;
        private String message;
        private Long applicationId;
        private String visitorName;
        private String visitorPhone;
        private String visitArea;

        public static QrCodeValidationResult success(Long applicationId, String visitorName, String visitorPhone, String visitArea) {
            QrCodeValidationResult result = new QrCodeValidationResult();
            result.valid = true;
            result.expired = false;
            result.message = "验证成功";
            result.applicationId = applicationId;
            result.visitorName = visitorName;
            result.visitorPhone = visitorPhone;
            result.visitArea = visitArea;
            return result;
        }

        public static QrCodeValidationResult expired() {
            QrCodeValidationResult result = new QrCodeValidationResult();
            result.valid = false;
            result.expired = true;
            result.message = "二维码佸凡杩囨湡";
            return result;
        }

        public static QrCodeValidationResult invalid() {
            QrCodeValidationResult result = new QrCodeValidationResult();
            result.valid = false;
            result.expired = false;
            result.message = "二维码佹棤鏁?;
            return result;
        }

        // Getters
        public boolean isValid() { return valid; }
        public boolean isExpired() { return expired; }
        public String getMessage() { return message; }
        public Long getApplicationId() { return applicationId; }
        public String getVisitorName() { return visitorName; }
        public String getVisitorPhone() { return visitorPhone; }
        public String getVisitArea() { return visitArea; }
    }

}


