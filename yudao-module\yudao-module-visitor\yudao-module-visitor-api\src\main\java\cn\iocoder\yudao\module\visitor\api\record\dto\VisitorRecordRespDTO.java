package cn.iocoder.yudao.module.visitor.api.record.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 访客记录 Response DTO
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Data
public class VisitorRecordRespDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 申请单ID
     */
    private Long applicationId;

    /**
     * 访客姓名
     */
    private String visitorName;

    /**
     * 访客电话
     */
    private String visitorPhone;

    /**
     * 操作类型：1-入园 2-出园
     */
    private Integer operationType;

    /**
     * 操作时间
     */
    private LocalDateTime operationTime;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 门岗位置
     */
    private String gateLocation;

    /**
     * 验证方式：1-二维码 2-手动 3-人脸识别
     */
    private Integer verificationMethod;

    /**
     * 车牌号
     */
    private String vehiclePlate;

    /**
     * 车辆照片URL
     */
    private String vehiclePhoto;

    /**
     * 现场访客照片URL
     */
    private String visitorPhoto;

    /**
     * 异常情况描述
     */
    private String abnormalInfo;

    /**
     * 异常照片URL数组
     */
    private List<String> abnormalPhotos;

    /**
     * 体温（摄氏度）
     */
    private BigDecimal temperature;

    /**
     * 健康状态：1-正常 2-异常
     */
    private Integer healthStatus;

    /**
     * 安检结果：1-通过 2-未通过
     */
    private Integer securityCheckResult;

    /**
     * 备注信息
     */
    private String remarks;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
