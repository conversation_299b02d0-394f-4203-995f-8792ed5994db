package cn.iocoder.yudao.module.visitor.api.record;

import cn.iocoder.yudao.module.visitor.api.record.dto.VisitorRecordRespDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 访客记录 API 接口
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
public interface VisitorRecordApi {

    /**
     * 根据申请ID获取访客记录
     *
     * @param applicationId 申请ID
     * @return 访客记录列表
     */
    List<VisitorRecordRespDTO> getVisitorRecordsByApplicationId(Long applicationId);

    /**
     * 根据时间范围获取访客记录
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 访客记录列表
     */
    List<VisitorRecordRespDTO> getVisitorRecordsByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取访客当前状态
     *
     * @param applicationId 申请ID
     * @return 访客当前状态：1-未入园 2-已入园 3-已出园
     */
    Integer getVisitorCurrentStatus(Long applicationId);

}
