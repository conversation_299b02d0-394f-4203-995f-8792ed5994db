# 修复文件编码问题的PowerShell脚本
# 解析FIX_NOTE.txt并重建有问题的文件

param(
    [string]$FixNoteFile = "issues\FIX_NOTE.txt",
    [switch]$DryRun = $false
)

Write-Host "开始解析编码问题文件..." -ForegroundColor Green

# 检查FIX_NOTE.txt文件是否存在
if (-not (Test-Path $FixNoteFile)) {
    Write-Error "找不到文件: $FixNoteFile"
    exit 1
}

# 读取并解析JSON文件
try {
    $content = Get-Content $FixNoteFile -Raw -Encoding UTF8
    # 将内容包装成数组格式以便解析
    $jsonContent = "[$content]"
    $errors = $jsonContent | ConvertFrom-Json
    Write-Host "成功解析 $($errors.Count) 个错误记录" -ForegroundColor Yellow
} catch {
    Write-Error "解析JSON文件失败: $($_.Exception.Message)"
    exit 1
}

# 提取唯一的文件路径，保持首次出现的顺序
$uniqueFiles = @()
$seenFiles = @{}

foreach ($error in $errors) {
    if ($error.resource -and -not $seenFiles.ContainsKey($error.resource)) {
        # 转换绝对路径为相对路径
        $relativePath = $error.resource -replace "^/D:/work/code/ptl/zc/ruoyi-vue-pro-yunqu-park/", ""
        $relativePath = $relativePath -replace "^D:\\work\\code\\ptl\\zc\\ruoyi-vue-pro-yunqu-park\\", ""
        $relativePath = $relativePath -replace "\\", "/"
        
        $uniqueFiles += $relativePath
        $seenFiles[$error.resource] = $true
    }
}

Write-Host "发现 $($uniqueFiles.Count) 个唯一的有问题文件:" -ForegroundColor Cyan
foreach ($file in $uniqueFiles) {
    Write-Host "  - $file" -ForegroundColor White
}

if ($DryRun) {
    Write-Host "DryRun模式，不执行实际操作" -ForegroundColor Yellow
    exit 0
}

# 处理每个文件
$successCount = 0
$failCount = 0

foreach ($file in $uniqueFiles) {
    Write-Host "`n处理文件: $file" -ForegroundColor Cyan
    
    try {
        # 检查文件是否存在
        if (-not (Test-Path $file)) {
            Write-Warning "文件不存在，跳过: $file"
            continue
        }
        
        # 使用git获取文件的最新正确版本
        Write-Host "  从git获取正确版本..." -ForegroundColor Yellow
        $gitContent = git show "HEAD:$file" 2>$null
        
        if ($LASTEXITCODE -ne 0) {
            Write-Warning "  无法从git获取文件内容，尝试使用当前版本修复编码"
            $gitContent = Get-Content $file -Raw -Encoding UTF8
        }
        
        if ([string]::IsNullOrEmpty($gitContent)) {
            Write-Warning "  获取的文件内容为空，跳过"
            $failCount++
            continue
        }
        
        # 备份原文件
        $backupFile = "$file.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        Write-Host "  备份原文件到: $backupFile" -ForegroundColor Yellow
        Copy-Item $file $backupFile -Force
        
        # 删除原文件
        Write-Host "  删除损坏的文件..." -ForegroundColor Yellow
        Remove-Item $file -Force
        
        # 重建文件，确保UTF-8编码无BOM
        Write-Host "  重建文件为UTF-8无BOM编码..." -ForegroundColor Yellow
        $utf8NoBom = New-Object System.Text.UTF8Encoding $false
        [System.IO.File]::WriteAllText((Resolve-Path $file).Path, $gitContent, $utf8NoBom)
        
        # 验证文件编码
        $testContent = Get-Content $file -Raw -Encoding UTF8
        if ($testContent -eq $gitContent) {
            Write-Host "  ✓ 文件重建成功" -ForegroundColor Green
            $successCount++
        } else {
            Write-Warning "  文件内容验证失败"
            $failCount++
        }
        
    } catch {
        Write-Error "  处理文件失败: $($_.Exception.Message)"
        $failCount++
    }
}

Write-Host "`n修复完成!" -ForegroundColor Green
Write-Host "成功: $successCount 个文件" -ForegroundColor Green
Write-Host "失败: $failCount 个文件" -ForegroundColor Red

if ($failCount -eq 0) {
    Write-Host "所有文件修复成功，建议运行编译检查验证结果" -ForegroundColor Green
} else {
    Write-Host "部分文件修复失败，请检查错误信息" -ForegroundColor Yellow
}
