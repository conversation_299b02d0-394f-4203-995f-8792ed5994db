package cn.iocoder.yudao.module.visitor.convert.training;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.module.visitor.controller.admin.training.vo.VisitorTrainingCreateReqVO;
import cn.iocoder.yudao.module.visitor.controller.admin.training.vo.VisitorTrainingRespVO;
import cn.iocoder.yudao.module.visitor.controller.admin.training.vo.VisitorTrainingUpdateReqVO;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorTrainingDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 访客培训 Convert
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Mapper
public interface VisitorTrainingConvert {

    VisitorTrainingConvert INSTANCE = Mappers.getMapper(VisitorTrainingConvert.class);

    VisitorTrainingDO convert(VisitorTrainingCreateReqVO bean);

    VisitorTrainingDO convert(VisitorTrainingUpdateReqVO bean);

    VisitorTrainingRespVO convert(VisitorTrainingDO bean);

    List<VisitorTrainingRespVO> convertList(List<VisitorTrainingDO> list);

    PageResult<VisitorTrainingRespVO> convertPage(PageResult<VisitorTrainingDO> page);

    // 鑷畾涔夋槧灏勬柟娉?    default Integer map(Boolean value) {
        return value != null && value ? 1 : 0;
    }

    default Boolean map(Integer value) {
        return value != null && value == 1;
    }

    default String map(List<VisitorTrainingDO.ExamQuestion> value) {
        return value != null ? JsonUtils.toJsonString(value) : null;
    }

    default List<VisitorTrainingDO.ExamQuestion> mapToExamQuestions(String value) {
        return value != null ? JsonUtils.parseArray(value, VisitorTrainingDO.ExamQuestion.class) : null;
    }

}

