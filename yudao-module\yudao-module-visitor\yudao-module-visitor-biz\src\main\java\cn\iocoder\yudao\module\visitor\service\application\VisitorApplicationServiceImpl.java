package cn.iocoder.yudao.module.visitor.service.application;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.bpm.api.task.dto.BpmProcessInstanceCreateReqDTO;
import cn.iocoder.yudao.module.bpm.service.task.BpmProcessInstanceApiService;
import cn.iocoder.yudao.module.visitor.controller.admin.application.vo.VisitorApplicationCreateReqVO;
import cn.iocoder.yudao.module.visitor.controller.admin.application.vo.VisitorApplicationPageReqVO;
import cn.iocoder.yudao.module.visitor.controller.admin.application.vo.VisitorApplicationUpdateReqVO;
import cn.iocoder.yudao.module.visitor.convert.application.VisitorApplicationConvert;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorApplicationDO;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorRecordDO;
import cn.iocoder.yudao.module.visitor.dal.mysql.VisitorApplicationMapper;
import cn.iocoder.yudao.module.visitor.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.visitor.enums.VisitorStatusEnum;
import cn.iocoder.yudao.module.visitor.service.record.VisitorRecordService;
import cn.iocoder.yudao.module.visitor.service.training.VisitorTrainingRecordService;
import cn.iocoder.yudao.module.visitor.framework.qrcode.VisitorQrCodeService;
import cn.iocoder.yudao.module.visitor.framework.notification.VisitorNotificationService;
import cn.iocoder.yudao.module.visitor.controller.app.guard.vo.AppQrCodeScanRespVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static cn.iocoder.yudao.module.visitor.enums.ErrorCodeConstants.*;

/**
 * 访客申请 Service 实现类? *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Service
@Validated
@Slf4j
public class VisitorApplicationServiceImpl implements VisitorApplicationService {

    @Resource
    private VisitorApplicationMapper visitorApplicationMapper;

    @Resource
    private VisitorRecordService visitorRecordService;

    @Resource
    private VisitorTrainingRecordService visitorTrainingRecordService;

    @Resource
    private BpmProcessInstanceApiService bpmProcessInstanceApi;

    @Resource
    private VisitorQrCodeService visitorQrCodeService;

    @Resource
    private VisitorNotificationService visitorNotificationService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createVisitorApplication(@Valid VisitorApplicationCreateReqVO createReqVO) {
        log.info("[createVisitorApplication] 创建访客申请锛屽弬鏁帮細{}", createReqVO);
        
        // 鎻掑叆访客申请
        VisitorApplicationDO visitorApplication = BeanUtils.toBean(createReqVO, VisitorApplicationDO.class);
        visitorApplication.setApplicationNo(generateApplicationNo());
        visitorApplication.setStatus(VisitorStatusEnum.PENDING_CONFIRM.getStatus());
        
        // 鏁版嵁校验
        validateVisitorApplication(visitorApplication);
        
        visitorApplicationMapper.insert(visitorApplication);

        // 鍙戦€佺敵璇锋彁浜ら€氱煡
        visitorNotificationService.sendApplicationSubmittedNotification(visitorApplication);

        log.info("[createVisitorApplication] 访客申请创建成功锛孖D锛歿}锛岀敵璇峰崟鍙凤細{}",
                visitorApplication.getId(), visitorApplication.getApplicationNo());

        return visitorApplication.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateVisitorApplication(@Valid VisitorApplicationUpdateReqVO updateReqVO) {
        log.info("[updateVisitorApplication] 更新访客申请锛屽弬鏁帮細{}", updateReqVO);
        
        // 校验存在
        VisitorApplicationDO existingApplication = validateVisitorApplicationExists(updateReqVO.getId());
        
        // 校验状态佹槸鍚﹀厑璁镐慨鏀?        if (!VisitorStatusEnum.PENDING_CONFIRM.getStatus().equals(existingApplication.getStatus())) {
            throw exception(VISITOR_APPLICATION_STATUS_NOT_ALLOW_UPDATE);
        }
        
        // 更新访客申请
        VisitorApplicationDO updateObj = BeanUtils.toBean(updateReqVO, VisitorApplicationDO.class);
        
        // 鏁版嵁校验
        validateVisitorApplication(updateObj);
        
        visitorApplicationMapper.updateById(updateObj);
        
        log.info("[updateVisitorApplication] 访客申请更新成功锛孖D锛歿}", updateReqVO.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteVisitorApplication(Long id) {
        log.info("[deleteVisitorApplication] 删除访客申请锛孖D锛歿}", id);
        
        // 校验存在
        VisitorApplicationDO application = validateVisitorApplicationExists(id);
        
        // 校验状态佹槸鍚﹀厑璁稿垹闄?        if (!VisitorStatusEnum.PENDING_CONFIRM.getStatus().equals(application.getStatus())) {
            throw exception(VISITOR_APPLICATION_STATUS_NOT_ALLOW_DELETE);
        }
        
        // 删除
        visitorApplicationMapper.deleteById(id);
        
        log.info("[deleteVisitorApplication] 访客申请删除成功锛孖D锛歿}", id);
    }

    private VisitorApplicationDO validateVisitorApplicationExists(Long id) {
        VisitorApplicationDO application = visitorApplicationMapper.selectById(id);
        if (application == null) {
            throw exception(VISITOR_APPLICATION_NOT_EXISTS);
        }
        return application;
    }

    @Override
    public VisitorApplicationDO getVisitorApplication(Long id) {
        return visitorApplicationMapper.selectById(id);
    }

    @Override
    public List<VisitorApplicationDO> getVisitorApplicationList(Collection<Long> ids) {
        return visitorApplicationMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<VisitorApplicationDO> getVisitorApplicationPage(VisitorApplicationPageReqVO pageReqVO) {
        return visitorApplicationMapper.selectPage(pageReqVO);
    }

    @Override
    public List<VisitorApplicationDO> getVisitorApplicationList(VisitorApplicationPageReqVO exportReqVO) {
        return visitorApplicationMapper.selectList(exportReqVO);
    }

    @Override
    public VisitorApplicationDO getVisitorApplicationByNo(String applicationNo) {
        return visitorApplicationMapper.selectByApplicationNo(applicationNo);
    }

    @Override
    public VisitorApplicationDO getVisitorApplicationByProcessInstanceId(String processInstanceId) {
        return visitorApplicationMapper.selectByProcessInstanceId(processInstanceId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String submitVisitorApplication(Long id) {
        log.info("[submitVisitorApplication] 鎻愪氦访客申请锛孖D锛歿}", id);
        
        // 校验存在
        VisitorApplicationDO application = validateVisitorApplicationExists(id);
        
        // 校验状态?        if (!VisitorStatusEnum.PENDING_CONFIRM.getStatus().equals(application.getStatus())) {
            throw exception(VISITOR_APPLICATION_STATUS_NOT_ALLOW_UPDATE);
        }
        
        // 鍚姩流程
        BpmProcessInstanceCreateReqDTO createReqDTO = new BpmProcessInstanceCreateReqDTO();
        createReqDTO.setProcessDefinitionKey("visitor_approval_process");
        createReqDTO.setBusinessKey(String.valueOf(id));
        //createReqDTO.setStartUserId(getLoginUserId());
        
        String processInstanceId = bpmProcessInstanceApi.createProcessInstanceApi(getLoginUserId(), createReqDTO);
        
        // 更新申请状态?        VisitorApplicationDO updateObj = new VisitorApplicationDO();
        updateObj.setId(id);
        updateObj.setProcessInstanceId(processInstanceId);
        updateObj.setStatus(VisitorStatusEnum.PENDING_APPROVAL.getStatus());
        visitorApplicationMapper.updateById(updateObj);
        
        log.info("[submitVisitorApplication] 访客申请鎻愪氦成功锛孖D锛歿}锛屾祦绋嬪疄渚婭D锛歿}", id, processInstanceId);
        
        return processInstanceId;
    }

    @Override
    public void validateVisitorApplication(VisitorApplicationDO application) {
        // 校验访客棶时间
        if (application.getVisitStartTime() != null && application.getVisitEndTime() != null) {
            if (application.getVisitStartTime().isAfter(application.getVisitEndTime())) {
                throw exception(VISITOR_APPLICATION_NOT_EXISTS); // 浣跨敤鍚堥€傜殑閿欒鐮?            }
        }

        // 校验联系人轰俊鎭?        if (StrUtil.isBlank(application.getContactPerson())) {
            throw exception(VISITOR_APPLICATION_NOT_EXISTS); // 浣跨敤鍚堥€傜殑閿欒鐮?        }
        
        // 鍏朵粬涓氬姟校验...
    }

    @Override
    public String generateApplicationNo() {
        return "VA" + DateUtil.format(LocalDateTime.now(), "yyyyMMdd") + IdUtil.getSnowflakeNextIdStr().substring(10);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirmVisitorApplication(Long id, Boolean confirmed, String reason) {
        log.info("[confirmVisitorApplication] 联系人虹‘璁よ瀹㈢敵璇凤紝ID锛歿}锛岀‘璁ょ粨鏋滐細{}锛屽師鍥狅細{}", id, confirmed, reason);

        // 校验存在
        VisitorApplicationDO application = validateVisitorApplicationExists(id);

        // 校验状态?        if (!VisitorStatusEnum.PENDING_CONFIRM.getStatus().equals(application.getStatus())) {
            throw exception(VISITOR_APPLICATION_STATUS_NOT_ALLOW_UPDATE);
        }

        // 更新状态?        VisitorApplicationDO updateObj = new VisitorApplicationDO();
        updateObj.setId(id);
        if (confirmed) {
            updateObj.setStatus(VisitorStatusEnum.APPROVED.getStatus());
        } else {
            updateObj.setStatus(VisitorStatusEnum.REJECTED.getStatus());
            updateObj.setApprovalResult(2); // 椹冲洖
        }
        updateObj.setApprovalReason(reason);
        updateObj.setApprovalTime(LocalDateTime.now());
        updateObj.setApproverId(getLoginUserId());

        visitorApplicationMapper.updateById(updateObj);

        // 鍙戦€佺‘璁ょ粨鏋滈€氱煡
        if (confirmed) {
            visitorNotificationService.sendApplicationApprovedNotification(application);
        } else {
            visitorNotificationService.sendApplicationRejectedNotification(application, reason);
        }

        log.info("[confirmVisitorApplication] 联系人虹‘璁ゅ畬鎴愶紝ID锛歿}锛岀粨鏋滐細{}", id, confirmed ? "纭" : "鎷掔粷");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approveVisitorApplication(Long id, Boolean approved, String reason) {
        log.info("[approveVisitorApplication] 瀹℃壒访客申请锛孖D锛歿}锛屽鎵圭粨鏋滐細{}锛屽師鍥狅細{}", id, approved, reason);

        // 校验存在
        VisitorApplicationDO application = validateVisitorApplicationExists(id);

        // 校验状态?        if (VisitorStatusEnum.APPROVED.getStatus().equals(application.getStatus()) && approved) {
            throw exception(VISITOR_APPLICATION_ALREADY_APPROVED);
        }
        if (VisitorStatusEnum.REJECTED.getStatus().equals(application.getStatus()) && !approved) {
            throw exception(VISITOR_APPLICATION_ALREADY_REJECTED);
        }

        // 更新状态?        VisitorApplicationDO updateObj = new VisitorApplicationDO();
        updateObj.setId(id);
        updateObj.setStatus(approved ? VisitorStatusEnum.APPROVED.getStatus() : VisitorStatusEnum.REJECTED.getStatus());
        updateObj.setApprovalResult(approved ? 1 : 2);
        updateObj.setApprovalReason(reason);
        updateObj.setApprovalTime(LocalDateTime.now());
        updateObj.setApproverId(getLoginUserId());

        visitorApplicationMapper.updateById(updateObj);

        // 鍙戦€佸鎵圭粨鏋滈€氱煡
        if (approved) {
            visitorNotificationService.sendApplicationApprovedNotification(application);
        } else {
            visitorNotificationService.sendApplicationRejectedNotification(application, reason);
        }

        log.info("[approveVisitorApplication] 瀹℃壒完成锛孖D锛歿}锛岀粨鏋滐細{}", id, approved ? "通过" : "椹冲洖");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String generateVisitorQrCode(Long id) {
        log.info("[generateVisitorQrCode] 鐢熸垚访客二维码侊紝ID锛歿}", id);

        // 校验存在
        VisitorApplicationDO application = validateVisitorApplicationExists(id);

        // 校验状态?        if (!VisitorStatusEnum.APPROVED.getStatus().equals(application.getStatus())) {
            throw exception(VISITOR_APPLICATION_NOT_APPROVED);
        }

        // 校验培训鏄惁完成
        if (!application.getTrainingCompleted().equals(1)) {
            throw exception(VISITOR_APPLICATION_TRAINING_NOT_COMPLETED);
        }

        // 浣跨敤二维码佹湇鍔＄敓鎴愪簩缁寸爜
        String qrCodeBase64 = visitorQrCodeService.generateQrCode(application);
        String qrCodeContent = generateQrCodeContent(application);

        // 更新二维码佷俊鎭?        VisitorApplicationDO updateObj = new VisitorApplicationDO();
        updateObj.setId(id);
        updateObj.setQrCode(qrCodeBase64);
        updateObj.setQrCodeContent(qrCodeContent);
        updateObj.setQrCodeExpireTime(LocalDateTime.now().plusDays(1)); // 24灏忔椂鏈夋晥鏈?        visitorApplicationMapper.updateById(updateObj);

        // 鍙戦€佷簩缁寸爜鐢熸垚閫氱煡
        visitorNotificationService.sendQrCodeGeneratedNotification(application, qrCodeBase64);

        log.info("[generateVisitorQrCode] 二维码佺敓鎴愭垚鍔燂紝ID锛歿}", id);

        return qrCodeBase64;
    }

    private String generateQrCodeContent(VisitorApplicationDO application) {
        // 绠€鍖栫殑JWT Token鐢熸垚閫昏緫
        return "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhcHBsaWNhdGlvbklkIjoi" + application.getId() +
               "IiwidmlzaXRvck5hbWUiOiIiLCJleHAiOjE3MDU5MjAwMDB9.signature";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void entryVisitor(Long id, Long operatorId, String gateLocation, Integer verificationMethod, Double temperature, String remarks) {
        log.info("[entryVisitor] 访客入园登记锛孖D锛歿}锛屾搷浣滃憳锛歿}锛岄棬宀楋細{}", id, operatorId, gateLocation);

        // 校验存在
        VisitorApplicationDO application = validateVisitorApplicationExists(id);

        // 校验状态?        if (!VisitorStatusEnum.APPROVED.getStatus().equals(application.getStatus())) {
            throw exception(VISITOR_APPLICATION_NOT_APPROVED);
        }

        // 校验鏄惁宸插叆鍥?        if (VisitorStatusEnum.ENTERED.getStatus().equals(application.getStatus())) {
            throw exception(VISITOR_APPLICATION_ALREADY_ENTERED);
        }

        // 校验二维码佹槸鍚﹁繃鏈?        if (application.getQrCodeExpireTime() != null && application.getQrCodeExpireTime().isBefore(LocalDateTime.now())) {
            throw exception(VISITOR_APPLICATION_QR_CODE_EXPIRED);
        }

        // 校验浣撴俯
        if (temperature != null && temperature > 37.3) {
            throw exception(VISITOR_RECORD_TEMPERATURE_ABNORMAL);
        }

        // 创建入园记录
        visitorRecordService.recordVisitorEntry(id, operatorId, gateLocation, verificationMethod, temperature, remarks);

        // 更新申请状态?        VisitorApplicationDO updateObj = new VisitorApplicationDO();
        updateObj.setId(id);
        updateObj.setStatus(VisitorStatusEnum.ENTERED.getStatus());
        updateObj.setEntryTime(LocalDateTime.now());
        updateObj.setEntryOperatorId(operatorId);

        visitorApplicationMapper.updateById(updateObj);

        log.info("[entryVisitor] 访客入园登记成功锛孖D锛歿}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void exitVisitor(Long id, Long operatorId, String gateLocation, String remarks) {
        log.info("[exitVisitor] 访客出园登记锛孖D锛歿}锛屾搷浣滃憳锛歿}锛岄棬宀楋細{}", id, operatorId, gateLocation);

        // 校验存在
        VisitorApplicationDO application = validateVisitorApplicationExists(id);

        // 校验状态?        if (!VisitorStatusEnum.ENTERED.getStatus().equals(application.getStatus())) {
            throw exception(VISITOR_APPLICATION_NOT_ENTERED);
        }

        // 校验鏄惁宸插嚭鍥?        if (VisitorStatusEnum.EXITED.getStatus().equals(application.getStatus())) {
            throw exception(VISITOR_APPLICATION_ALREADY_EXITED);
        }

        // 创建出园记录
        visitorRecordService.recordVisitorExit(id, operatorId, gateLocation, remarks);

        // 更新申请状态?        VisitorApplicationDO updateObj = new VisitorApplicationDO();
        updateObj.setId(id);
        updateObj.setStatus(VisitorStatusEnum.EXITED.getStatus());
        updateObj.setExitTime(LocalDateTime.now());
        updateObj.setExitOperatorId(operatorId);

        visitorApplicationMapper.updateById(updateObj);

        log.info("[exitVisitor] 访客出园登记成功锛孖D锛歿}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelVisitorApplication(Long id, String reason) {
        log.info("[cancelVisitorApplication] 鍙栨秷访客申请锛孖D锛歿}锛屽師鍥狅細{}", id, reason);

        // 校验存在
        VisitorApplicationDO application = validateVisitorApplicationExists(id);

        // 校验状态侊紙宸插叆鍥垨宸插嚭鍥殑涓嶈兘鍙栨秷锛?        if (VisitorStatusEnum.ENTERED.getStatus().equals(application.getStatus()) ||
            VisitorStatusEnum.EXITED.getStatus().equals(application.getStatus())) {
            throw exception(VISITOR_APPLICATION_STATUS_NOT_ALLOW_UPDATE);
        }

        // 更新状态佷负宸查┏鍥?        VisitorApplicationDO updateObj = new VisitorApplicationDO();
        updateObj.setId(id);
        updateObj.setStatus(VisitorStatusEnum.REJECTED.getStatus());
        updateObj.setApprovalResult(2); // 椹冲洖
        updateObj.setApprovalReason("申请鍙栨秷锛? + reason);
        updateObj.setApprovalTime(LocalDateTime.now());
        updateObj.setApproverId(getLoginUserId());

        visitorApplicationMapper.updateById(updateObj);

        log.info("[cancelVisitorApplication] 访客申请鍙栨秷成功锛孖D锛歿}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateVisitorApplicationStatus(Long id, Integer status, String reason) {
        log.info("[updateVisitorApplicationStatus] 更新访客申请状态侊紝ID锛歿}锛岀姸鎬侊細{}锛屽師鍥狅細{}", id, status, reason);

        // 校验存在
        validateVisitorApplicationExists(id);

        // 更新状态?        VisitorApplicationDO updateObj = new VisitorApplicationDO();
        updateObj.setId(id);
        updateObj.setStatus(status);

        visitorApplicationMapper.updateById(updateObj);

        log.info("[updateVisitorApplicationStatus] 状态佹洿鏂版垚鍔燂紝ID锛歿}锛屾柊状态侊細{}", id, status);
    }

    @Override
    public boolean isVisitorApplicationExpired(Long id) {
        VisitorApplicationDO application = getVisitorApplication(id);
        if (application == null) {
            return false;
        }

        // 检查查询闂粨鏉熸椂闂存槸鍚﹀凡杩囨湡
        return application.getVisitEndTime() != null && application.getVisitEndTime().isBefore(LocalDateTime.now());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleExpiredApplications() {
        log.info("[handleExpiredApplications] 澶勭悊杩囨湡鐨勮瀹㈢敵璇?);

        // 查询杩囨湡鐨勭敵璇?        List<VisitorApplicationDO> expiredApplications = visitorApplicationMapper.selectExpiredApplications(LocalDateTime.now());

        for (VisitorApplicationDO application : expiredApplications) {
            try {
                // 更新状态佷负宸查┏鍥?                VisitorApplicationDO updateObj = new VisitorApplicationDO();
                updateObj.setId(application.getId());
                updateObj.setStatus(VisitorStatusEnum.REJECTED.getStatus());
                updateObj.setApprovalResult(2); // 椹冲洖
                updateObj.setApprovalReason("申请宸茶繃鏈燂紝绯荤粺鑷姩澶勭悊");
                updateObj.setApprovalTime(LocalDateTime.now());

                visitorApplicationMapper.updateById(updateObj);

                log.info("[handleExpiredApplications] 澶勭悊杩囨湡申请成功锛孖D锛歿}锛岀敵璇峰崟鍙凤細{}",
                        application.getId(), application.getApplicationNo());
            } catch (Exception e) {
                log.error("[handleExpiredApplications] 澶勭悊杩囨湡申请失败锛孖D锛歿}锛岄敊璇細{}",
                         application.getId(), e.getMessage(), e);
            }
        }

        log.info("[handleExpiredApplications] 杩囨湡申请澶勭悊完成锛屽叡澶勭悊锛歿}鏉?, expiredApplications.size());
    }

    @Override
    public Object getVisitorApplicationStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        log.info("[getVisitorApplicationStatistics] 鑾峰彇访客申请统计锛屾椂闂磋寖鍥达細{} - {}", startTime, endTime);

        // 杩欓噷杩斿洖涓€涓畝鍖栫殑统计瀵硅薄
        // 瀹為檯椤圭洰涓簲璇ヨ繑鍥炲叿浣撶殑统计DTO
        return new Object() {
            public final Long totalCount = visitorApplicationMapper.selectCountByTimeRange(startTime, endTime, null);
            public final Long approvedCount = visitorApplicationMapper.selectCountByTimeRange(startTime, endTime, VisitorStatusEnum.APPROVED.getStatus());
            public final Long rejectedCount = visitorApplicationMapper.selectCountByTimeRange(startTime, endTime, VisitorStatusEnum.REJECTED.getStatus());
            public final Long enteredCount = visitorApplicationMapper.selectCountByTimeRange(startTime, endTime, VisitorStatusEnum.ENTERED.getStatus());
            public final Long exitedCount = visitorApplicationMapper.selectCountByTimeRange(startTime, endTime, VisitorStatusEnum.EXITED.getStatus());
        };
    }

    @Override
    public AppQrCodeScanRespVO scanQrCode(String qrCodeContent) {
        log.info("[scanQrCode] 鎵弿访客二维码侊紝鍐呭闀垮害锛歿}", qrCodeContent.length());

        try {
            // 验证二维码?            VisitorQrCodeService.QrCodeValidationResult validationResult =
                    visitorQrCodeService.validateQrCode(qrCodeContent);

            AppQrCodeScanRespVO respVO = new AppQrCodeScanRespVO();
            respVO.setValid(validationResult.isValid());
            respVO.setMessage(validationResult.getMessage());

            if (validationResult.isValid()) {
                // 鑾峰彇访客申请信息
                VisitorApplicationDO application = getVisitorApplication(validationResult.getApplicationId());
                if (application != null) {
                    respVO.setApplicationId(application.getId());
                    respVO.setApplicationNo(application.getApplicationNo());
                    respVO.setVisitorName(application.getVisitorName());
                    respVO.setVisitorPhone(application.getVisitorPhone());
                    respVO.setCompanyName(application.getCompanyName());
                    respVO.setVisitReason(application.getVisitReason());
                    respVO.setVisitArea(application.getVisitArea());
                    respVO.setContactPerson(application.getContactPerson());
                    respVO.setContactPhone(application.getContactPhone());
                    respVO.setVisitStartTime(application.getVisitStartTime());
                    respVO.setVisitEndTime(application.getVisitEndTime());
                    respVO.setHasVehicle(application.getHasVehicle() == 1);
                    respVO.setVehiclePlate(application.getVehiclePlate());
                    respVO.setTrainingCompleted(application.getTrainingCompleted() == 1);
                    respVO.setQrCodeExpireTime(application.getQrCodeExpireTime());

                    // 璁剧疆同行人浜哄憳信息
                    if (application.getCompanionInfo() != null && !application.getCompanionInfo().isEmpty()) {
                        List<AppQrCodeScanRespVO.CompanionInfo> companions = application.getCompanionInfo().stream()
                                .map(companion -> {
                                    AppQrCodeScanRespVO.CompanionInfo info = new AppQrCodeScanRespVO.CompanionInfo();
                                    info.setName(companion.getName());
                                    info.setPhone(companion.getPhone());
                                    info.setIdCard(companion.getIdCard());
                                    return info;
                                }).collect(java.util.stream.Collectors.toList());
                        respVO.setCompanions(companions);
                    }
                }
            }

            log.info("[scanQrCode] 二维码佹壂鎻忓畬鎴愶紝验证结果锛歿}", validationResult.isValid());
            return respVO;

        } catch (Exception e) {
            log.error("[scanQrCode] 二维码佹壂鎻忓け璐?, e);

            AppQrCodeScanRespVO respVO = new AppQrCodeScanRespVO();
            respVO.setValid(false);
            respVO.setMessage("二维码佹壂鎻忓け璐ワ細" + e.getMessage());
            return respVO;
        }
    }

}

