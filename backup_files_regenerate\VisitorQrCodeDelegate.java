package cn.iocoder.yudao.module.visitor.framework.flowable.delegate;

import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorApplicationDO;
import cn.iocoder.yudao.module.visitor.framework.notification.VisitorNotificationService;
import cn.iocoder.yudao.module.visitor.service.application.VisitorApplicationService;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * 访客二维码佺敓鎴愬鎵樼被
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Component("visitorQrCodeDelegate")
@Slf4j
public class VisitorQrCodeDelegate implements JavaDelegate {

    @Resource
    private VisitorApplicationService visitorApplicationService;

    @Resource
    private VisitorNotificationService visitorNotificationService;

    @Override
    public void execute(DelegateExecution execution) {
        log.info("[execute] 鎵ц访客二维码佺敓鎴愬鎵橈紝流程实例ID锛歿}", execution.getProcessInstanceId());
        
        try {
            // 鑾峰彇流程鍙橀噺
            Object applicationIdObj = execution.getVariable("applicationId");
            
            if (applicationIdObj != null) {
                Long applicationId = Long.valueOf(applicationIdObj.toString());
                
                log.info("[execute] 申请ID锛歿}", applicationId);
                
                // 检查ユ槸鍚﹂渶瑕佺敓鎴愪簩缁寸爜
                Boolean needQrCode = (Boolean) execution.getVariable("needQrCode");
                if (needQrCode == null || !needQrCode) {
                    log.info("[execute] 鏃犻渶鐢熸垚二维码侊紝璺宠繃");
                    return;
                }
                
                // 检查ュ煿璁槸鍚﹀畬鎴?                Boolean trainingCompleted = (Boolean) execution.getVariable("trainingCompleted");
                if (trainingCompleted == null || !trainingCompleted) {
                    log.info("[execute] 培训鏈畬鎴愶紝鏆備笉鐢熸垚二维码?);
                    execution.setVariable("qrCodeGenerated", false);
                    return;
                }

                // 鐢熸垚二维码?                String qrCodeUrl = visitorApplicationService.generateVisitorQrCode(applicationId);
                
                // 鑾峰彇申请信息
                VisitorApplicationDO application = visitorApplicationService.getVisitorApplication(applicationId);
                
                // 鍙戦€佷簩缁寸爜鐢熸垚閫氱煡
                visitorNotificationService.sendQrCodeGeneratedNotification(application, qrCodeUrl);
                
                // 璁剧疆流程鍙橀噺
                execution.setVariable("qrCodeGenerated", true);
                execution.setVariable("qrCodeUrl", qrCodeUrl);
                execution.setVariable("qrCodeGenerateTime", java.time.LocalDateTime.now().toString());
                
                log.info("[execute] 二维码佺敓鎴愭垚鍔燂紝URL锛歿}", qrCodeUrl);
            }
            
            log.info("[execute] 访客二维码佺敓鎴愬鎵樻墽琛屽畬鎴?);

        } catch (Exception e) {
            log.error("[execute] 访客二维码佺敓鎴愬鎵樻墽琛屽け璐?, e);
            throw new RuntimeException("二维码佺敓鎴愬鎵樻墽琛屽け璐?, e);
        }
    }

}

