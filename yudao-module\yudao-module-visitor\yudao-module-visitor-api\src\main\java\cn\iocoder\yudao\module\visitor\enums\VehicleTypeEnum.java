package cn.iocoder.yudao.module.visitor.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 车辆类型枚举
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Getter
@AllArgsConstructor
public enum VehicleTypeEnum{

    NORMAL_CAR(1, "普通客车"),
    CONSTRUCTION_VEHICLE(2, "施工车");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(VehicleTypeEnum::getType).toArray();

    /**
     * 车辆类型
     */
    private final Integer type;
    /**
     * 车辆类型名称
     */
    private final String name;

    public static VehicleTypeEnum valueOf(Integer type) {
        return Arrays.stream(values()).filter(item -> item.getType().equals(type)).findFirst().orElse(null);
    }

}
