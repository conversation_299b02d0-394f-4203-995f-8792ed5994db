package cn.iocoder.yudao.module.visitor.dal.dataobject;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.visitor.enums.VisitorStatusEnum;
import cn.iocoder.yudao.module.visitor.enums.VisitorTypeEnum;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 访客申请 DO
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@TableName(value = "visitor_application", autoResultMap = true)
@KeySequence("visitor_application_seq") // 鐢ㄤ簬 Oracle銆丳ostgreSQL銆並ingbase銆丏B2銆丠2 鏁版嵁搴撶殑涓婚敭鑷銆傚鏋滄槸 MySQL 绛夋暟鎹簱锛屽彲涓嶅啓
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VisitorApplicationDO extends BaseDO {

    /**
     * 涓婚敭ID
     */
    @TableId
    private Long id;

    /**
     * 申请鍗曞彿
     */
    private String applicationNo;

    /**
     * 访客类型
     *
     * 鏋氫妇 {@link VisitorTypeEnum}
     */
    private Integer visitorType;

    /**
     * 鏉ヨ鍗曚綅
     */
    private String companyName;

    /**
     * 访客姓名
     */
    private String visitorName;

    /**
     * 联系人鏂瑰紡
     */
    private String visitorPhone;

    /**
     * 韬唤璇佸彿鐮?     */
    private String idCard;

    /**
     * 访客照片URL
     */
    private String visitorPhoto;

    /**
     * 鏉ヨ浜嬬敱
     */
    private String visitReason;

    /**
     * 璁块棶鐩殑锛?-鍙傝 2-鍟嗗姟 3-鏂藉伐 4-检查?     */
    private Integer visitPurpose;

    /**
     * 鍘傚唴联系人?     */
    private String contactPerson;

    /**
     * 联系人虹數璇?     */
    private String contactPhone;

    /**
     * 联系人洪儴闂↖D
     */
    private Long contactDeptId;

    /**
     * 联系人洪儴闂ㄥ悕绉?     */
    private String contactDeptName;

    /**
     * 棰勮鍒拌开始嬫椂闂?     */
    private LocalDateTime visitStartTime;

    /**
     * 棰勮鍒拌缁撴潫时间
     */
    private LocalDateTime visitEndTime;

    /**
     * 鍒拌厂区
     */
    private String visitArea;

    /**
     * 鍒拌鏂瑰紡锛?-闅忚溅入园 2-鍚勮嚜入园
     */
    private Integer visitMethod;

    /**
     * 鏄惁椹捐溅锛?-鍚?1-鏄?     */
    private Integer hasVehicle;

    /**
     * 车牌鍙?     */
    private String vehiclePlate;

    /**
     * 车辆类型锛?-鏅€氬杞?2-鏂藉伐杞?     */
    private Integer vehicleType;

    /**
     * 行驶证佺紪鍙?     */
    private String vehicleLicense;

    /**
     * 车辆照片URL
     */
    private String vehiclePhoto;

    /**
     * 同行人浜烘暟閲?     */
    private Integer companionCount;

    /**
     * 同行人浜轰俊鎭疛SON锛歔{name,phone,idCard,photo}]
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<CompanionInfo> companionInfo;

    /**
     * 鏄惁闇€瑕佷綇瀹匡細0-鍚?1-鏄?     */
    private Integer needAccommodation;

    /**
     * 鏄惁鍓嶅線楗爞就餐锛?-鍚?1-鏄?     */
    private Integer needDining;

    /**
     * 鏄惁完成培训锛?-鍚?1-鏄?     */
    private Integer trainingCompleted;

    /**
     * 培训完成时间
     */
    private LocalDateTime trainingTime;

    /**
     * 培训绛惧瓧鍥剧墖URL
     */
    private String trainingSignature;

    /**
     * 閫氳二维码乁RL
     */
    private String qrCode;

    /**
     * 二维码佸唴瀹癸紙JWT Token锛?     */
    private String qrCodeContent;

    /**
     * 二维码佽繃鏈熸椂闂?     */
    private LocalDateTime qrCodeExpireTime;

    /**
     * 状态?     *
     * 鏋氫妇 {@link VisitorStatusEnum}
     */
    private Integer status;

    /**
     * 瀹℃壒缁撴灉锛?-通过 2-椹冲洖
     */
    private Integer approvalResult;

    /**
     * 瀹℃壒鎰忚
     */
    private String approvalReason;

    /**
     * 瀹℃壒时间
     */
    private LocalDateTime approvalTime;

    /**
     * 瀹℃壒浜篒D
     */
    private Long approverId;

    /**
     * 瀹℃壒浜哄鍚?     */
    private String approverName;

    /**
     * 入园时间
     */
    private LocalDateTime entryTime;

    /**
     * 出园时间
     */
    private LocalDateTime exitTime;

    /**
     * 入园操作员業D
     */
    private Long entryOperatorId;

    /**
     * 出园操作员業D
     */
    private Long exitOperatorId;

    /**
     * Flowable娴佺▼瀹炰緥ID
     */
    private String processInstanceId;

    /**
     * 鎵垮寘鍟嗛」鐩甀D锛堟柦宸ユ壙鍖呭晢涓撶敤锛?     */
    private Long contractorProjectId;

    /**
     * 紧急联系人
     */
    private String emergencyContact;

    /**
     * 绱ф€ヨ仈绯荤數璇?     */
    private String emergencyPhone;

    /**
     * 特殊要求
     */
    private String specialRequirements;

    /**
     * 同行人浜轰俊鎭唴閮ㄧ被
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CompanionInfo {
        /**
         * 姓名
         */
        private String name;
        /**
         * 电话
         */
        private String phone;
        /**
         * 韬唤璇佸彿
         */
        private String idCard;
        /**
         * 照片URL
         */
        private String photo;
    }

}

