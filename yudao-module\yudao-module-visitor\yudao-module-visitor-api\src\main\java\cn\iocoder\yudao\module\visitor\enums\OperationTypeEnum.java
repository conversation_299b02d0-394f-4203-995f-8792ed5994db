package cn.iocoder.yudao.module.visitor.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 操作类型枚举
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Getter
@AllArgsConstructor
public enum OperationTypeEnum{

    ENTRY(1, "入园"),
    EXIT(2, "出园");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(OperationTypeEnum::getType).toArray();

    /**
     * 操作类型
     */
    private final Integer type;
    /**
     * 操作类型名称
     */
    private final String name;

    public static OperationTypeEnum valueOf(Integer type) {
        return Arrays.stream(values()).filter(item -> item.getType().equals(type)).findFirst().orElse(null);
    }

}
