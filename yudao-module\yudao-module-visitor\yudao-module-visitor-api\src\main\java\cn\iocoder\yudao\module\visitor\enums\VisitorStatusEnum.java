package cn.iocoder.yudao.module.visitor.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 访客状态枚举
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Getter
@AllArgsConstructor
public enum VisitorStatusEnum{

    PENDING_CONFIRM(1, "待确认"),
    PENDING_APPROVAL(2, "待审批"),
    APPROVED(3, "已审批"),
    REJECTED(4, "已驳回"),
    ENTERED(5, "已入园"),
    EXITED(6, "已出园");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(VisitorStatusEnum::getStatus).toArray();

    /**
     * 状态值
     */
    private final Integer status;
    /**
     * 状态名称
     */
    private final String name;



    public static VisitorStatusEnum valueOf(Integer status) {
        return Arrays.stream(values()).filter(item -> item.getStatus().equals(status)).findFirst().orElse(null);
    }

}
