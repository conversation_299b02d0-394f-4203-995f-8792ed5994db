#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件编码修复脚本
用于修复Java文件中的编码问题和乱码
"""

import json
import os
import shutil
import chardet
from pathlib import Path
import re

class EncodingFixer:
    def __init__(self, workspace_root):
        self.workspace_root = Path(workspace_root)
        self.backup_dir = self.workspace_root / "backup_files"
        self.backup_dir.mkdir(exist_ok=True)
        
        # 乱码修复映射表
        self.garbled_mappings = {
            '娴佺▼': '流程',
            '鍗旾D': '单ID',
            '缁撴灉': '结果',
            '浜篒D': '人ID',
            '浜哄鍚?': '人姓名',
            '浜虹被鍨?': '人类型',
            '浜哄鍚嶇О': '人姓名',
            '浜虹被鍨嬪瀷': '人类型',
            '鎿嶄綔鍛?': '操作员',
            '鎿嶄綔': '操作',
            '璁板綍': '记录',
            '鍒嗛〉': '分页',
            '绠＄悊': '管理',
            '鍚庡彴': '后台',
            '璁块': '访客',
            '瀹炰緥': '实例',
            '浠诲姟': '任务',
            '绫诲瀷': '类型',
            '鏃堕棿': '时间',
            '鍒涘缓': '创建',
            '鏇存柊': '更新',
            '鍒犻櫎': '删除',
            '鏌ヨ': '查询',
            '浜哄鍚?': '人姓名',
            '寮犱笁': '张三',
            '浜虹被鍨?': '人类型'
        }
    
    def extract_file_paths_from_fix_note(self, fix_note_path):
        """从FIX_NOTE.txt中提取文件路径"""
        try:
            with open(fix_note_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 修复JSON格式 - 添加数组包装
            if not content.strip().startswith('['):
                content = '[' + content
            if not content.strip().endswith(']'):
                content = content + ']'

            # 解析JSON数组
            json_data = json.loads(content)

            file_paths = set()
            for item in json_data:
                if 'resource' in item:
                    resource_path = item['resource']
                    # 转换Windows路径为相对路径
                    if resource_path.startswith('/D:/work/code/ptl/zc/ruoyi-vue-pro-yunqu-park/'):
                        relative_path = resource_path.replace('/D:/work/code/ptl/zc/ruoyi-vue-pro-yunqu-park/', '')
                        file_paths.add(relative_path.replace('/', os.sep))

            return sorted(list(file_paths))

        except Exception as e:
            print(f"解析FIX_NOTE.txt失败: {e}")
            print(f"错误详情: {str(e)}")
            # 尝试手动提取文件路径
            try:
                with open(fix_note_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                file_paths = set()
                # 使用正则表达式提取resource路径
                import re
                pattern = r'"resource":\s*"([^"]+)"'
                matches = re.findall(pattern, content)

                for match in matches:
                    if match.startswith('/D:/work/code/ptl/zc/ruoyi-vue-pro-yunqu-park/'):
                        relative_path = match.replace('/D:/work/code/ptl/zc/ruoyi-vue-pro-yunqu-park/', '')
                        file_paths.add(relative_path.replace('/', os.sep))

                return sorted(list(file_paths))
            except Exception as e2:
                print(f"手动提取也失败: {e2}")
                return []
    
    def detect_encoding(self, file_path):
        """检测文件编码"""
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read()
            
            # 检测BOM
            if raw_data.startswith(b'\xef\xbb\xbf'):
                return 'utf-8-sig'
            elif raw_data.startswith(b'\xff\xfe'):
                return 'utf-16-le'
            elif raw_data.startswith(b'\xfe\xff'):
                return 'utf-16-be'
            
            # 使用chardet检测
            result = chardet.detect(raw_data)
            return result['encoding'] if result['encoding'] else 'utf-8'
        
        except Exception as e:
            print(f"检测编码失败 {file_path}: {e}")
            return 'utf-8'
    
    def fix_garbled_text(self, content):
        """修复乱码文本"""
        for garbled, correct in self.garbled_mappings.items():
            content = content.replace(garbled, correct)
        
        # 修复特殊的乱码模式
        # 修复引号问题
        content = re.sub(r'浜哄鍚?\s*,\s*example\s*=\s*"([^"]*)"', r'人姓名", example = "\1"', content)
        content = re.sub(r'浜虹被鍨?\s*,\s*example\s*=\s*"([^"]*)"', r'人类型", example = "\1"', content)
        
        return content
    
    def backup_file(self, file_path):
        """备份文件"""
        try:
            backup_path = self.backup_dir / file_path.name
            counter = 1
            while backup_path.exists():
                backup_path = self.backup_dir / f"{file_path.stem}_{counter}{file_path.suffix}"
                counter += 1
            
            shutil.copy2(file_path, backup_path)
            print(f"已备份: {file_path} -> {backup_path}")
            return backup_path
        
        except Exception as e:
            print(f"备份失败 {file_path}: {e}")
            return None
    
    def fix_file_encoding(self, file_path):
        """修复单个文件的编码"""
        full_path = self.workspace_root / file_path
        
        if not full_path.exists():
            print(f"文件不存在: {full_path}")
            return False
        
        print(f"\n处理文件: {file_path}")
        
        # 备份原文件
        backup_path = self.backup_file(full_path)
        if not backup_path:
            return False
        
        try:
            # 检测原始编码
            original_encoding = self.detect_encoding(full_path)
            print(f"检测到编码: {original_encoding}")
            
            # 尝试读取文件内容
            content = None
            encodings_to_try = [original_encoding, 'utf-8', 'gbk', 'gb2312', 'utf-8-sig']
            
            for encoding in encodings_to_try:
                try:
                    with open(full_path, 'r', encoding=encoding) as f:
                        content = f.read()
                    print(f"成功使用编码读取: {encoding}")
                    break
                except UnicodeDecodeError:
                    continue
            
            if content is None:
                print(f"无法读取文件内容: {full_path}")
                return False
            
            # 修复乱码
            original_content = content
            content = self.fix_garbled_text(content)
            
            if content != original_content:
                print("已修复乱码")
            
            # 移除BOM并以UTF-8保存
            with open(full_path, 'w', encoding='utf-8', newline='') as f:
                f.write(content)
            
            print(f"已保存为UTF-8编码: {full_path}")
            return True
        
        except Exception as e:
            print(f"处理文件失败 {full_path}: {e}")
            # 恢复备份
            try:
                shutil.copy2(backup_path, full_path)
                print(f"已恢复备份: {full_path}")
            except:
                pass
            return False
    
    def process_all_files(self, fix_note_path):
        """处理所有文件"""
        print("开始批量修复文件编码...")
        
        # 提取文件路径
        file_paths = self.extract_file_paths_from_fix_note(fix_note_path)
        
        if not file_paths:
            print("未找到需要处理的文件")
            return
        
        print(f"找到 {len(file_paths)} 个需要处理的文件")
        
        success_count = 0
        failed_files = []
        
        for file_path in file_paths:
            if self.fix_file_encoding(file_path):
                success_count += 1
            else:
                failed_files.append(file_path)
        
        # 生成处理报告
        print(f"\n=== 处理完成 ===")
        print(f"成功处理: {success_count} 个文件")
        print(f"处理失败: {len(failed_files)} 个文件")
        
        if failed_files:
            print("\n失败的文件:")
            for file_path in failed_files:
                print(f"  - {file_path}")
        
        print(f"\n备份文件保存在: {self.backup_dir}")

def main():
    workspace_root = r"d:\work\code\ptl\zc\ruoyi-vue-pro-yunqu-park"
    fix_note_path = os.path.join(workspace_root, "issues", "FIX_NOTE.txt")
    
    fixer = EncodingFixer(workspace_root)
    fixer.process_all_files(fix_note_path)

if __name__ == "__main__":
    main()
